/**
 * Shared utility functions for resource processors
 * Eliminates code duplication across specialized processors
 */

/**
 * Gets the size of a resource value
 */
export function getResourceSize(resourceValue: any): number {
    if (!resourceValue) return 0;
    
    if (Buffer.isBuffer(resourceValue)) {
        return resourceValue.length;
    }
    
    if (typeof resourceValue === 'string') {
        return Buffer.byteLength(resourceValue, 'utf8');
    }
    
    if (resourceValue.buffer && Buffer.isBuffer(resourceValue.buffer)) {
        return resourceValue.buffer.length;
    }
    
    return 0;
}

/**
 * Determines if a resource is compressed
 */
export function isResourceCompressed(resourceValue: any): boolean {
    if (!resourceValue) return false;
    
    // Check for common compression signatures
    if (Buffer.isBuffer(resourceValue) && resourceValue.length > 4) {
        const header = resourceValue.readUInt32LE(0);
        // Common compression signatures (simplified check)
        return header === 0x04034b50 || // ZIP
               header === 0x474e5089 || // PNG (often compressed)
               header === 0x38464947;   // GIF
    }
    
    return false;
}

/**
 * Creates a basic ProcessedResource structure
 */
export function createBaseProcessedResource(
    entry: any,
    type: string,
    processorName: string,
    specialized: boolean = true
): import('./types').ProcessedResource {
    const resourceKey = entry.key;
    const resourceValue = entry.value;
    
    return {
        id: entry.id?.toString() || `${resourceKey.type}-${resourceKey.group}-${resourceKey.instance}`,
        type,
        size: getResourceSize(resourceValue),
        compressed: isResourceCompressed(resourceValue),
        metadata: {
            resourceType: resourceKey.type,
            group: resourceKey.group,
            instance: resourceKey.instance,
            specialized,
            processorUsed: processorName
        },
        issues: []
    };
}
