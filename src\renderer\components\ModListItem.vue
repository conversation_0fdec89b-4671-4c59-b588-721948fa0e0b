<template>
  <div class="mod-list-item">
    <div class="mod-list-item__main">
      <div class="mod-list-item__info">
        <h4 class="mod-list-item__name">{{ modData?.fileName || 'Unknown' }}</h4>
        <div class="mod-list-item__meta">
          <span v-if="modData?.author" class="author">by {{ modData.author }}</span>
          <span class="content-type">{{ getContentType(modData) }}</span>
          <span class="file-size">{{ formatFileSize(modData?.fileSize || 0) }}</span>
          <span class="file-type" :class="`type-${(modData?.fileExtension || '.package').slice(1)}`">
            {{ modData?.fileExtension || '.package' }}
          </span>
          <span v-if="(modData?.fileExtension || '.package') === '.ts4script'" class="script-indicator">
            Script Mod
          </span>
        </div>
      </div>
      

    </div>
  </div>
</template>

<script setup lang="ts">


defineProps<{
  modData: any;
}>();

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

const getContentType = (modData: any): string => {
  const fileName = (modData?.fileName || '').toLowerCase();

  // Specific content type detection
  if (fileName.includes('hair')) return 'Hair';
  if (fileName.includes('clothing') || fileName.includes('outfit')) return 'Clothing';
  if (fileName.includes('skin')) return 'Skin';
  if (fileName.includes('makeup')) return 'Makeup';
  if (fileName.includes('trait')) return 'Traits';
  if (fileName.includes('career')) return 'Careers';
  if (fileName.includes('skill')) return 'Skills';
  if (fileName.includes('furniture')) return 'Furniture';
  if (fileName.includes('object')) return 'Objects';
  if (fileName.includes('lot')) return 'Lots';
  if (fileName.includes('world')) return 'Worlds';

  // Fallback to category
  if (fileName.includes('cas')) return 'Create-a-Sim';
  if (fileName.includes('build')) return 'Build/Buy';
  if (fileName.includes('gameplay')) return 'Gameplay';
  if ((modData?.fileExtension || '.package') === '.ts4script') return 'Script';

  return 'General';
};
</script>

<style scoped>
.mod-list-item {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  transition: all var(--duration-150) var(--ease-out);
}

.mod-list-item:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-sm);
}

.mod-list-item__main {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mod-list-item__info {
  flex: 1;
  min-width: 0;
}

.mod-list-item__name {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mod-list-item__meta {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.author {
  color: var(--text-accent);
  font-weight: var(--font-medium);
}

.file-size {
  font-family: var(--font-family-mono);
}

.content-type {
  background: #f0f4f8;
  color: #334155;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
  font-size: 0.75rem;
}

.script-indicator {
  background: #fce4ec;
  color: #c2185b;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
  font-size: 0.75rem;
}

.file-type {
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
}

.file-type.type-package {
  background: var(--sims-blue-bg);
  color: var(--sims-blue);
}

.file-type.type-ts4script {
  background: var(--sims-purple-bg);
  color: var(--sims-purple);
}

.mod-list-item__indicators {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}
</style>
