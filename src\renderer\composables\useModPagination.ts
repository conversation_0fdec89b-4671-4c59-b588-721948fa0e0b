import { computed, watch, shallowRef, type Ref } from 'vue';
import type { ModData } from '../types/dashboard';

export function useModPagination(
  filteredMods: Ref<ModData[]>,
  currentPage: Ref<number>,
  itemsPerPage: Ref<number>
) {
  // Use shallowRef for paginated results to optimize performance
  const paginatedMods = shallowRef<ModData[]>([]);

  // Computed properties
  const totalPages = computed(() =>
    Math.ceil(filteredMods.value.length / itemsPerPage.value)
  );

  const startIndex = computed(() =>
    (currentPage.value - 1) * itemsPerPage.value
  );

  const endIndex = computed(() =>
    Math.min(startIndex.value + itemsPerPage.value, filteredMods.value.length)
  );

  // Watch for changes and update paginated mods
  watch(
    [filteredMods, currentPage, itemsPerPage],
    () => {
      if (!filteredMods.value || !Array.isArray(filteredMods.value)) {
        paginatedMods.value = [];
        return;
      }

      const start = startIndex.value;
      const end = endIndex.value;
      paginatedMods.value = filteredMods.value.slice(start, end);

      // Reset to page 1 if current page is beyond total pages
      if (currentPage.value > totalPages.value && totalPages.value > 0) {
        currentPage.value = 1;
      }
    },
    { immediate: true }
  );

  // Navigation methods
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page;
    }
  };

  const goToPreviousPage = () => {
    if (currentPage.value > 1) {
      currentPage.value--;
    }
  };

  const goToNextPage = () => {
    if (currentPage.value < totalPages.value) {
      currentPage.value++;
    }
  };

  const resetToFirstPage = () => {
    currentPage.value = 1;
  };

  return {
    // State
    paginatedMods,

    // Computed
    totalPages,
    startIndex,
    endIndex,

    // Methods
    goToPage,
    goToPreviousPage,
    goToNextPage,
    resetToFirstPage
  };
}