#!/usr/bin/env tsx

/**
 * Test script to verify RLE2 thumbnail extraction using S4TK DdsImage
 */

import * as fs from 'fs';
import * as path from 'path';
import * as zlib from 'zlib';
import { Package } from '@s4tk/models';
import { BinaryResourceType } from '@s4tk/models/enums';
import { DdsImage } from '@s4tk/images';

const TIFFANY_PACKAGE_PATH = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods\\KiaraZurk_TiffanyHairstyle\\KiaraZurk_TiffanyHairstyle.package';

/**
 * Constructs a complete DDS file from raw DXT texture data
 * This is needed for RLE2 images which contain only the compressed texture data without DDS headers
 */
function constructDdsFromRawDxtData(dxtData: Buffer): Buffer {
    // Assume DXT5 format for Sims 4 RLE2 images (most common for CC thumbnails)
    // Standard thumbnail dimensions for Sims 4 CC
    const width = 256;
    const height = 256;

    // Calculate linear size for DXT5: (width/4) * (height/4) * 16 bytes per block
    const blocksWide = Math.max(1, Math.floor((width + 3) / 4));
    const blocksHigh = Math.max(1, Math.floor((height + 3) / 4));
    const linearSize = blocksWide * blocksHigh * 16; // DXT5 uses 16 bytes per 4x4 block

    // Create DDS file buffer: 4 bytes magic + 124 bytes header + texture data
    const totalSize = 4 + 124 + dxtData.length;
    const ddsBuffer = Buffer.alloc(totalSize);

    let offset = 0;

    // Write DDS magic number "DDS " (0x20534444)
    ddsBuffer.writeUInt32LE(0x20534444, offset);
    offset += 4;

    // Write DDS_HEADER (124 bytes)
    // dwSize (must be 124)
    ddsBuffer.writeUInt32LE(124, offset);
    offset += 4;

    // dwFlags (required flags for DXT5 texture)
    const flags = 0x1 | 0x2 | 0x4 | 0x1000 | 0x80000; // CAPS | HEIGHT | WIDTH | PIXELFORMAT | LINEARSIZE
    ddsBuffer.writeUInt32LE(flags, offset);
    offset += 4;

    // dwHeight
    ddsBuffer.writeUInt32LE(height, offset);
    offset += 4;

    // dwWidth
    ddsBuffer.writeUInt32LE(width, offset);
    offset += 4;

    // dwPitchOrLinearSize (linear size for compressed texture)
    ddsBuffer.writeUInt32LE(linearSize, offset);
    offset += 4;

    // dwDepth (unused for 2D texture)
    ddsBuffer.writeUInt32LE(0, offset);
    offset += 4;

    // dwMipMapCount (1 for single mip level)
    ddsBuffer.writeUInt32LE(1, offset);
    offset += 4;

    // dwReserved1[11] (44 bytes of zeros)
    for (let i = 0; i < 11; i++) {
        ddsBuffer.writeUInt32LE(0, offset);
        offset += 4;
    }

    // DDS_PIXELFORMAT (32 bytes)
    // dwSize (must be 32)
    ddsBuffer.writeUInt32LE(32, offset);
    offset += 4;

    // dwFlags (DDPF_FOURCC for compressed format)
    ddsBuffer.writeUInt32LE(0x4, offset);
    offset += 4;

    // dwFourCC ("DXT5" = 0x35545844)
    ddsBuffer.writeUInt32LE(0x35545844, offset);
    offset += 4;

    // dwRGBBitCount (unused for compressed format)
    ddsBuffer.writeUInt32LE(0, offset);
    offset += 4;

    // dwRBitMask (unused for compressed format)
    ddsBuffer.writeUInt32LE(0, offset);
    offset += 4;

    // dwGBitMask (unused for compressed format)
    ddsBuffer.writeUInt32LE(0, offset);
    offset += 4;

    // dwBBitMask (unused for compressed format)
    ddsBuffer.writeUInt32LE(0, offset);
    offset += 4;

    // dwABitMask (unused for compressed format)
    ddsBuffer.writeUInt32LE(0, offset);
    offset += 4;

    // dwCaps (DDSCAPS_TEXTURE)
    ddsBuffer.writeUInt32LE(0x1000, offset);
    offset += 4;

    // dwCaps2 (unused)
    ddsBuffer.writeUInt32LE(0, offset);
    offset += 4;

    // dwCaps3 (unused)
    ddsBuffer.writeUInt32LE(0, offset);
    offset += 4;

    // dwCaps4 (unused)
    ddsBuffer.writeUInt32LE(0, offset);
    offset += 4;

    // dwReserved2 (unused)
    ddsBuffer.writeUInt32LE(0, offset);
    offset += 4;

    // Copy the raw DXT texture data
    dxtData.copy(ddsBuffer, offset);

    console.log(`🔧 [RLE2] Constructed DDS file: ${width}x${height} DXT5, ${dxtData.length} bytes texture data, ${totalSize} bytes total`);

    return ddsBuffer;
}

/**
 * Extracts Buffer from S4TK resource (copied from ThumbnailExtractionService)
 */
function extractBufferFromResource(resource: any): Buffer | null {
    try {
        // Check if resource.value is already a Buffer
        if (resource.value instanceof Buffer) {
            return resource.value;
        }

        // Check if it's an S4TK resource with buffer cache
        if (resource.value && typeof resource.value === 'object') {
            const rawResource = resource.value as any;

            // Try to access the buffer from various S4TK resource structures
            if (rawResource._bufferCache?.buffer instanceof Buffer) {
                return rawResource._bufferCache.buffer;
            }

            if (rawResource.buffer instanceof Buffer) {
                return rawResource.buffer;
            }

            // Try to get the buffer using S4TK's buffer property
            if (typeof rawResource.getBuffer === 'function') {
                return rawResource.getBuffer();
            }
        }

        console.warn('Could not extract buffer from resource:', typeof resource.value);
        return null;
    } catch (error) {
        console.warn('Error extracting buffer from resource:', error);
        return null;
    }
}

async function testRle2Extraction() {
    console.log('🧪 Testing RLE2 Thumbnail Extraction...\n');
    
    try {
        if (!fs.existsSync(TIFFANY_PACKAGE_PATH)) {
            console.log(`❌ Package file not found: ${TIFFANY_PACKAGE_PATH}`);
            return;
        }

        // Read and parse the package
        const packageBuffer = fs.readFileSync(TIFFANY_PACKAGE_PATH);
        const s4tkPackage = Package.from(packageBuffer);
        
        console.log(`📦 Package loaded: ${path.basename(TIFFANY_PACKAGE_PATH)}`);
        console.log(`🔢 Total resources: ${s4tkPackage.size}`);

        // Find RLE2 image resources
        const rle2Resources = [];
        for (const entry of s4tkPackage.entries.values()) {
            if (entry.key.type === BinaryResourceType.Rle2Image) {
                rle2Resources.push(entry);
            }
        }

        console.log(`🖼️ Found ${rle2Resources.length} RLE2 image resources\n`);

        if (rle2Resources.length === 0) {
            console.log('❌ No RLE2 resources found to test');
            return;
        }

        // Test the first few RLE2 resources
        const resourcesToTest = rle2Resources.slice(0, 3);
        
        for (let i = 0; i < resourcesToTest.length; i++) {
            const entry = resourcesToTest[i];
            console.log(`\n--- Testing RLE2 Resource ${i + 1} ---`);
            console.log(`Key: ${entry.key.type.toString(16)}-${entry.key.group.toString(16)}-${entry.key.instance.toString(16)}`);

            // Extract buffer properly
            const buffer = extractBufferFromResource(entry);
            if (!buffer) {
                console.log(`❌ Failed to extract buffer from resource ${i + 1}`);
                continue;
            }

            console.log(`Size: ${buffer.length} bytes`);

            try {
                // RLE2 appears to be zlib-compressed DDS, try to decompress first
                console.log(`   First 4 bytes: ${Array.from(buffer.slice(0, 4)).map(b => '0x' + b.toString(16)).join(', ')}`);

                let decompressedBuffer: Buffer;
                try {
                    // Try zlib decompression
                    decompressedBuffer = zlib.inflateSync(buffer);
                    console.log(`✅ Successfully decompressed RLE2 data`);
                    console.log(`   Original size: ${buffer.length} bytes`);
                    console.log(`   Decompressed size: ${decompressedBuffer.length} bytes`);
                } catch (zlibError) {
                    console.log(`❌ Failed to decompress with zlib: ${zlibError.message}`);
                    // Try direct DDS parsing as fallback
                    decompressedBuffer = buffer;
                }

                // The decompressed buffer contains raw DXT texture data without DDS headers
                // We need to construct a proper DDS file with headers for S4TK to process
                const ddsBuffer = constructDdsFromRawDxtData(decompressedBuffer);

                // Try to decode with S4TK DdsImage
                const ddsImage = DdsImage.from(ddsBuffer);
                
                console.log(`✅ Successfully parsed with S4TK DdsImage`);
                console.log(`   Shuffled: ${ddsImage.isShuffled}`);
                
                // Convert to Jimp
                const jimpImage = ddsImage.toJimp();
                console.log(`✅ Successfully converted to Jimp`);
                console.log(`   Dimensions: ${jimpImage.getWidth()}x${jimpImage.getHeight()}`);
                
                // Convert to PNG buffer
                const pngBuffer = await jimpImage.getBufferAsync('image/png');
                console.log(`✅ Successfully converted to PNG`);
                console.log(`   PNG size: ${pngBuffer.length} bytes`);
                
                // Save a test image to verify it works
                const outputPath = `test-rle2-${i + 1}.png`;
                fs.writeFileSync(outputPath, pngBuffer);
                console.log(`💾 Saved test image: ${outputPath}`);
                
            } catch (error) {
                console.log(`❌ Failed to decode RLE2 resource ${i + 1}:`, error.message);
            }
        }

        console.log('\n🎉 RLE2 extraction test completed!');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Run the test
testRle2Extraction().catch(console.error);
