#!/usr/bin/env tsx

/**
 * Test RLE2 extraction with the Tiffany hairstyle package (25 hair color variations)
 */

import * as fs from 'fs';
import * as path from 'path';
import { ThumbnailExtractionService } from './src/services/visual/ThumbnailExtractionService';

const TIFFANY_PACKAGE_PATH = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods\\KiaraZurk_TiffanyHairstyle\\KiaraZurk_TiffanyHairstyle.package';

async function testTiffanyRle2Extraction() {
    console.log('🧪 Testing Tiffany Hairstyle RLE2 Extraction (25 hair color variations)...\n');
    
    try {
        if (!fs.existsSync(TIFFANY_PACKAGE_PATH)) {
            console.log(`❌ Package file not found: ${TIFFANY_PACKAGE_PATH}`);
            return;
        }

        // Read the file
        const buffer = fs.readFileSync(TIFFANY_PACKAGE_PATH);
        console.log(`📦 Testing with: ${path.basename(TIFFANY_PACKAGE_PATH)}`);
        console.log(`📏 File size: ${(buffer.length / 1024).toFixed(2)} KB`);
        
        // Extract thumbnails with RLE2 priority
        console.log('\n🔍 Extracting RLE2 thumbnails...');
        const result = await ThumbnailExtractionService.extractThumbnails(
            buffer,
            path.basename(TIFFANY_PACKAGE_PATH),
            {
                maxThumbnails: 25, // Extract all 25 hair color variations
                preferredFormat: 'png',
                maxWidth: 256,
                maxHeight: 256,
                prioritizeCasThumbnails: false, // Prioritize RLE2 images
                includeTextures: true,
                generateFallbacks: true
            }
        );
        
        console.log('\n📊 Extraction Results:');
        console.log(`✅ Success: ${result.success}`);
        console.log(`🖼️ Thumbnails extracted: ${result.thumbnailsExtracted}`);
        console.log(`📈 Total images found: ${result.totalImagesFound}`);
        console.log(`⏱️ Processing time: ${result.processingTime.toFixed(2)}ms`);
        console.log(`⚡ Average time per thumbnail: ${(result.processingTime / result.thumbnailsExtracted).toFixed(2)}ms`);
        
        if (result.errors.length > 0) {
            console.log('\n❌ Errors:');
            result.errors.forEach(error => console.log(`  - ${error}`));
        }
        
        console.log('\n🎨 Extracted Thumbnails:');
        result.thumbnails.forEach((thumbnail, index) => {
            console.log(`  ${index + 1}. ${thumbnail.resourceType} (${thumbnail.extractionMethod})`);
            console.log(`     Size: ${thumbnail.width}x${thumbnail.height}, Confidence: ${thumbnail.confidence}%`);
            console.log(`     Category: ${thumbnail.category}, Quality: ${thumbnail.isHighQuality ? 'High' : 'Low'}`);
            console.log(`     Data length: ${thumbnail.imageData.length} chars`);
            console.log(`     Data preview: ${thumbnail.imageData.substring(0, 50)}...`);
        });
        
        // Count RLE2 extractions
        const rle2Count = result.thumbnails.filter(t => t.extractionMethod === 'rle2_conversion').length;
        console.log(`\n🎯 RLE2 Extractions: ${rle2Count}/${result.thumbnailsExtracted}`);
        
        if (rle2Count >= 20) {
            console.log('🎉 SUCCESS: Extracted 20+ hair color variations from RLE2 images!');
        } else if (rle2Count >= 10) {
            console.log('✅ GOOD: Extracted 10+ hair color variations from RLE2 images');
        } else {
            console.log('⚠️ LIMITED: Only extracted a few RLE2 images');
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Run the test
testTiffanyRle2Extraction().catch(console.error);
