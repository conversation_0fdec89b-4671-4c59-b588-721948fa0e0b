import { FileTypeDetector } from '../core/FileTypeDetector';
import { quickAnalysisService } from '../core/QuickAnalysisService';
import { detailedAnalysisService } from '../core/DetailedAnalysisService';
import { UnifiedErrorHandler, UnifiedErrorCategory } from '../../shared/UnifiedErrorHandler';
import { FileType, ModCategory } from '../../../types/analysis';
import type { 
    EnhancedDetailedAnalysisResult, 
    BatchAnalysisOptions,
    CancellationToken 
} from '../../../types/analysis-results';

/**
 * Service responsible for batch analysis operations
 * Handles concurrent processing of multiple files with progress tracking
 */
export class BatchAnalysisService {

    /**
     * Analyzes multiple files with progress reporting and cancellation support
     */
    public async analyzeBatch(
        files: { buffer: Buffer; filePath: string }[], 
        options: BatchAnalysisOptions = {}
    ): Promise<EnhancedDetailedAnalysisResult[]> {
        const { concurrency = 3, onProgress, cancellationToken } = options;
        const results: EnhancedDetailedAnalysisResult[] = [];
        
        try {
            // Process files in batches to control concurrency
            const batches = this.chunkArray(files, concurrency);
            let completed = 0;
            
            for (const batch of batches) {
                // Check for cancellation
                if (cancellationToken?.isCancelled) {
                    throw new Error('Batch analysis cancelled');
                }
                
                // Process batch in parallel
                const batchPromises = batch.map(file => 
                    this.analyzeFile(file.buffer, file.filePath, cancellationToken)
                        .then(result => {
                            completed++;
                            onProgress?.(completed, files.length);
                            return result;
                        })
                        .catch(error => {
                            completed++;
                            onProgress?.(completed, files.length);
                            return this.createErrorResult(file, error);
                        })
                );
                
                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);
            }
            
            // Detect cross-mod conflicts and dependencies
            await this.detectCrossModIssues(results);
            
            return results;
            
        } catch (error) {
            const unifiedError = UnifiedErrorHandler.createError(
                error,
                'BatchAnalysisService.analyzeBatch',
                'batch',
                UnifiedErrorCategory.PACKAGE_LOADING
            );
            console.error('Batch analysis failed:', unifiedError.message);
            throw error;
        }
    }

    /**
     * Analyzes a single file as part of batch processing
     */
    private async analyzeFile(
        buffer: Buffer, 
        filePath: string, 
        cancellationToken?: CancellationToken
    ): Promise<EnhancedDetailedAnalysisResult> {
        // Perform quick analysis first
        const quickResult = await quickAnalysisService.analyzeAsync(buffer, filePath, cancellationToken);
        
        // Then detailed analysis
        return await detailedAnalysisService.analyzeAsync(buffer, filePath, quickResult, cancellationToken);
    }

    /**
     * Creates error result for failed file analysis
     */
    private createErrorResult(
        file: { buffer: Buffer; filePath: string },
        error: any
    ): EnhancedDetailedAnalysisResult {
        const errorInfo = UnifiedErrorHandler.createError(
            error,
            'BatchAnalysisService.analyzeFile',
            file.filePath,
            UnifiedErrorCategory.PACKAGE_LOADING
        );
        const fileMetadata = FileTypeDetector.analyzeFile(file.filePath, file.buffer.length);
        
        return {
            filePath: file.filePath,
            fileType: fileMetadata.type as FileType,
            category: ModCategory.UNKNOWN,
            subcategory: 'error',
            fileSize: file.buffer.length,
            resourceCount: 0,
            isOverride: false,
            resources: [],
            dependencies: [],
            conflicts: [],
            metadata: { error: errorInfo.message },
            specializedResources: [],
            resourceValidation: { isValid: false, issues: [errorInfo.message] },
            performanceMetrics: {
                totalTime: 0,
                quickAnalysisTime: 0,
                detailedAnalysisTime: 0,
                resourceCount: 0
            }
        } as EnhancedDetailedAnalysisResult;
    }

    /**
     * Detects conflicts and dependencies across multiple mods
     */
    private async detectCrossModIssues(results: EnhancedDetailedAnalysisResult[]): Promise<void> {
        // Group by category for conflict detection
        const scriptMods = results.filter(r => r.fileType === FileType.SCRIPT);
        const overrideMods = results.filter(r => r.isOverride);

        // Detect script mod conflicts
        for (const mod of scriptMods) {
            const fileName = mod.filePath.toLowerCase();

            // Check for UI cheat conflicts
            if (fileName.includes('ui_cheat') || fileName.includes('uicheat')) {
                const otherUiCheats = scriptMods.filter(other =>
                    other !== mod &&
                    (other.filePath.toLowerCase().includes('ui_cheat') ||
                        other.filePath.toLowerCase().includes('uicheat'))
                );

                if (otherUiCheats.length > 0) {
                    mod.conflicts.push(`Conflicts with: ${otherUiCheats.map(m => m.filePath).join(', ')}`);
                }
            }
        }

        // Detect override conflicts
        for (const mod of overrideMods) {
            const otherOverrides = overrideMods.filter(other => other !== mod);
            if (otherOverrides.length > 0) {
                mod.conflicts.push(`Potential conflicts with ${otherOverrides.length} other override mod(s)`);
            }
        }
    }

    /**
     * Chunks an array into smaller arrays for batch processing
     */
    private chunkArray<T>(array: T[], size: number): T[][] {
        const chunks: T[][] = [];
        for (let i = 0; i < array.length; i += size) {
            chunks.push(array.slice(i, i + size));
        }
        return chunks;
    }

    /**
     * Creates cancellation token for batch operations
     */
    public createCancellationToken(): CancellationToken {
        let cancelled = false;
        
        return {
            get isCancelled() { return cancelled; },
            cancel() { cancelled = true; }
        };
    }
}

// Export singleton instance
export const batchAnalysisService = new BatchAnalysisService();