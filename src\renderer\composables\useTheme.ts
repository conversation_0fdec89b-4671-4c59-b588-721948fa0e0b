/**
 * Vue 3 Composition API Theme Management
 * Advanced OKLCH-based color theming with dynamic switching
 * Accessibility-compliant color contrast management
 */

import { ref, computed, watch, onMounted, readonly } from 'vue';

export type ColorScheme = 'light' | 'dark' | 'auto';
export type ThemeVariant = 'default' | 'high-contrast' | 'colorful';

interface ThemeState {
  colorScheme: ColorScheme;
  variant: ThemeVariant;
  reducedMotion: boolean;
  highContrast: boolean;
}

// Reactive theme state
const themeState = ref<ThemeState>({
  colorScheme: 'auto',
  variant: 'default',
  reducedMotion: false,
  highContrast: false,
});

// System preferences detection
const systemPrefersDark = ref(false);
const systemPrefersReducedMotion = ref(false);
const systemPrefersHighContrast = ref(false);

// Computed effective theme
const effectiveColorScheme = computed(() => {
  if (themeState.value.colorScheme === 'auto') {
    return systemPrefersDark.value ? 'dark' : 'light';
  }
  return themeState.value.colorScheme;
});

// Color manipulation utilities using OKLCH
const adjustColorLightness = (color: string, adjustment: number): string => {
  // Extract OKLCH values and adjust lightness
  const oklchMatch = color.match(/oklch\(([^)]+)\)/);
  if (!oklchMatch) return color;
  
  const values = oklchMatch[1].split(' ');
  const lightness = parseFloat(values[0]);
  const newLightness = Math.max(0, Math.min(1, lightness + adjustment));
  
  return `oklch(${newLightness} ${values[1]} ${values[2]})`;
};

const getContrastColor = (backgroundColor: string): string => {
  // Simple contrast color calculation for OKLCH
  const oklchMatch = backgroundColor.match(/oklch\(([^)]+)\)/);
  if (!oklchMatch) return 'var(--text-primary)';
  
  const values = oklchMatch[1].split(' ');
  const lightness = parseFloat(values[0]);
  
  // Use threshold of 0.6 for better contrast
  return lightness > 0.6 ? 'var(--charcoal-900)' : 'var(--charcoal-50)';
};

// Dynamic color generation
const generateColorScale = (baseHue: number, baseLightness: number, baseChroma: number) => {
  const scale = {
    50: `oklch(0.97 ${baseChroma * 0.1} ${baseHue})`,
    100: `oklch(0.94 ${baseChroma * 0.2} ${baseHue})`,
    200: `oklch(0.88 ${baseChroma * 0.4} ${baseHue})`,
    300: `oklch(0.82 ${baseChroma * 0.6} ${baseHue})`,
    400: `oklch(0.75 ${baseChroma * 0.8} ${baseHue})`,
    500: `oklch(${baseLightness} ${baseChroma} ${baseHue})`,
    600: `oklch(${baseLightness * 0.9} ${baseChroma * 1.1} ${baseHue})`,
    700: `oklch(${baseLightness * 0.75} ${baseChroma * 0.9} ${baseHue})`,
    800: `oklch(${baseLightness * 0.6} ${baseChroma * 0.7} ${baseHue})`,
    900: `oklch(${baseLightness * 0.45} ${baseChroma * 0.5} ${baseHue})`,
    950: `oklch(${baseLightness * 0.3} ${baseChroma * 0.3} ${baseHue})`,
  };
  return scale;
};

// Theme application
const applyTheme = () => {
  const root = document.documentElement;
  
  // Apply color scheme class
  root.classList.remove('light', 'dark');
  root.classList.add(effectiveColorScheme.value);
  
  // Apply variant class
  root.classList.remove('default', 'high-contrast', 'colorful');
  root.classList.add(themeState.value.variant);
  
  // Apply accessibility preferences
  if (themeState.value.reducedMotion || systemPrefersReducedMotion.value) {
    root.classList.add('reduced-motion');
  } else {
    root.classList.remove('reduced-motion');
  }
  
  if (themeState.value.highContrast || systemPrefersHighContrast.value) {
    root.classList.add('high-contrast');
  } else {
    root.classList.remove('high-contrast');
  }
};

// System preference detection
const detectSystemPreferences = () => {
  if (typeof window !== 'undefined') {
    // Dark mode detection
    const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
    systemPrefersDark.value = darkModeQuery.matches;
    darkModeQuery.addEventListener('change', (e) => {
      systemPrefersDark.value = e.matches;
    });
    
    // Reduced motion detection
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    systemPrefersReducedMotion.value = reducedMotionQuery.matches;
    reducedMotionQuery.addEventListener('change', (e) => {
      systemPrefersReducedMotion.value = e.matches;
    });
    
    // High contrast detection
    const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
    systemPrefersHighContrast.value = highContrastQuery.matches;
    highContrastQuery.addEventListener('change', (e) => {
      systemPrefersHighContrast.value = e.matches;
    });
  }
};

// Persistence
const STORAGE_KEY = 'simonitor-theme';

const saveTheme = () => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(themeState.value));
  }
};

const loadTheme = () => {
  if (typeof window !== 'undefined') {
    const saved = localStorage.getItem(STORAGE_KEY);
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        themeState.value = { ...themeState.value, ...parsed };
      } catch (error) {
        console.warn('Failed to parse saved theme:', error);
      }
    }
  }
};

// Main composable
export function useTheme() {
  // Initialize on mount
  onMounted(() => {
    detectSystemPreferences();
    loadTheme();
    applyTheme();
  });
  
  // Watch for changes and apply
  watch(
    [themeState, systemPrefersDark, systemPrefersReducedMotion, systemPrefersHighContrast],
    () => {
      applyTheme();
      saveTheme();
    },
    { deep: true }
  );
  
  // Theme control methods
  const setColorScheme = (scheme: ColorScheme) => {
    themeState.value.colorScheme = scheme;
  };
  
  const setVariant = (variant: ThemeVariant) => {
    themeState.value.variant = variant;
  };
  
  const toggleColorScheme = () => {
    if (themeState.value.colorScheme === 'auto') {
      setColorScheme('light');
    } else if (themeState.value.colorScheme === 'light') {
      setColorScheme('dark');
    } else {
      setColorScheme('auto');
    }
  };
  
  const setReducedMotion = (enabled: boolean) => {
    themeState.value.reducedMotion = enabled;
  };
  
  const setHighContrast = (enabled: boolean) => {
    themeState.value.highContrast = enabled;
  };
  
  // Utility methods
  const isDark = computed(() => effectiveColorScheme.value === 'dark');
  const isLight = computed(() => effectiveColorScheme.value === 'light');
  const isReducedMotion = computed(() => 
    themeState.value.reducedMotion || systemPrefersReducedMotion.value
  );
  const isHighContrast = computed(() => 
    themeState.value.highContrast || systemPrefersHighContrast.value
  );
  
  return {
    // State
    themeState: readonly(themeState),
    effectiveColorScheme,
    systemPrefersDark: readonly(systemPrefersDark),
    systemPrefersReducedMotion: readonly(systemPrefersReducedMotion),
    systemPrefersHighContrast: readonly(systemPrefersHighContrast),
    
    // Computed
    isDark,
    isLight,
    isReducedMotion,
    isHighContrast,
    
    // Methods
    setColorScheme,
    setVariant,
    toggleColorScheme,
    setReducedMotion,
    setHighContrast,
    
    // Utilities
    adjustColorLightness,
    getContrastColor,
    generateColorScale,
  };
}

// Export for global use
export default useTheme;
