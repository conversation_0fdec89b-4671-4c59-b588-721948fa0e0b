<template>
  <div class="app">
    <!-- Main Content -->
    <main class="app-main">




      <!-- Enhanced ModDashboard Interface -->
      <ModDashboard
        :mods="analysisResults"
        :is-loading="isAnalyzing"
        @analyze-folder="selectAndAnalyzeFolder"
        @export-results="exportResults"
        @open-settings="openSettings"
      />

      <!-- Error Display -->
      <div v-if="analysisError" class="container">
        <section class="error-section mb-xl">
          <div class="card">
            <div class="error-content">
              <div class="flex items-center gap-md mb-md">
                <ExclamationTriangleIcon class="w-6 h-6 text-error" />
                <h3 class="font-medium text-error">Analysis Error</h3>
              </div>
              <p class="text-muted mb-md">{{ analysisError }}</p>
              <button class="btn btn-secondary btn-sm" @click="clearError">
                Dismiss
              </button>
            </div>
          </div>
        </section>
      </div>
    </main>

    <!-- Footer -->
    <footer class="app-footer">
      <div class="container">
        <div class="text-center py-md text-sm text-muted">
          <p>Simonitor - Built with ❤️ for the Sims 4 community</p>
        </div>
      </div>
    </footer>

    <!-- Settings Modal -->
    <Modal
      :is-open="isSettingsOpen"
      title="Settings"
      @close="closeSettings"
    >
      <AppSettings
        @settings-changed="handleSettingsChanged"
        ref="settingsRef"
      />
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue';
import {
  FolderOpenIcon,
  ArrowDownTrayIcon,
  CogIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline';

import ModDashboard from './components/ModDashboard.vue';
import FileUpload from './components/FileUpload.vue';
import Modal from './components/Modal.vue';
import AppSettings from './components/AppSettings.vue';

// Types
import type { AnalyzedPackage, ResourceInfo } from '../types/analysis';

// Reactive state
const selectedFiles = ref<File[]>([]);
const analysisResults = ref<any[]>([]);
const isAnalyzing = ref(false);

const analyzedCount = ref(0);
const analysisError = ref<string | null>(null);
const fileUploadRef = ref();
const isSettingsOpen = ref(false);
const settingsRef = ref();
const currentModsFolder = ref<string>('');

// Watch for analysis results changes
watch(analysisResults, (newResults, oldResults) => {
  // Analysis results changed - trigger reactivity
}, { immediate: true, deep: true });

// Watch for isAnalyzing changes
watch(isAnalyzing, (newAnalyzing, oldAnalyzing) => {
  // Loading state changed - trigger reactivity
}, { immediate: true });

// Event handlers
function handleFilesSelected(files: File[]) {
  selectedFiles.value = files;
  // Clear previous results when new files are selected
  if (files.length === 0) {
    analysisResults.value = [];
    analysisError.value = null;
  }
}

async function handleAnalyzeRequested(files: File[]) {
  if (files.length === 0) return;

  if (!window.electronAPI) {
    analysisError.value = 'Electron API not available. Please run in Electron app.';
    return;
  }

  isAnalyzing.value = true;
  analyzedCount.value = 0;
  analysisResults.value = [];
  analysisError.value = null;

  try {
    // Analyze files one by one
    for (const file of files) {
      const filePath = (file as any).path;
      
      // Create a promise to handle the async analysis
      const analysisPromise = new Promise<AnalyzedPackage>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Analysis timeout'));
        }, 30000); // 30 second timeout
        
        const handleResult = (result: any) => {
          clearTimeout(timeout);
          window.electronAPI.offAnalysisResult?.(handleResult);
          
          if (result.error) {
            reject(new Error(result.error));
          } else {
            resolve(result);
          }
        };
        
        window.electronAPI.onAnalysisResult(handleResult);
        window.electronAPI.analyzePackage(filePath);
      });
      
      try {
        const result = await analysisPromise;
        analysisResults.value.push(result);
        analyzedCount.value++;
      } catch (error) {
        analysisError.value = `Error analyzing ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        break;
      }
    }
  } catch (error) {
    analysisError.value = error instanceof Error ? error.message : 'An unexpected error occurred';
  } finally {
    isAnalyzing.value = false;
  }
}

function clearError() {
  analysisError.value = null;
}



// Settings handlers
function openSettings() {
  isSettingsOpen.value = true;
}

function closeSettings() {
  isSettingsOpen.value = false;
}

function handleSettingsChanged(settings: any) {
  // Handle settings changes - could be used to update app behavior
}

// New enhanced methods for folder analysis
async function selectAndAnalyzeFolder() {
  try {
    if (!window.electronAPI) {
      analysisError.value = 'Electron API not available. Please run in Electron app.';
      return;
    }

    isAnalyzing.value = true;
    analysisError.value = null;

    // Open folder selection dialog
    const folderResult = await window.electronAPI.selectModsFolder();

    if (!folderResult.success) {
      if (folderResult.error !== 'No folder selected') {
        analysisError.value = folderResult.error;
      }
      isAnalyzing.value = false;
      return;
    }

    currentModsFolder.value = folderResult.path;

    // Analyze the entire folder
    const analysisResult = await window.electronAPI.analyzeModsFolder(folderResult.path);

    // Always try to process data, even if there were some analysis errors
    if (analysisResult.success || (analysisResult.data && Array.isArray(analysisResult.data))) {

      // Check if data exists and is an array
      if (analysisResult.data && Array.isArray(analysisResult.data)) {
        const processedResults = analysisResult.data
          .filter((result: any) => result && (result.filePath || result.fileName)) // Filter out completely invalid results
          .map((result: any) => ({
          ...result,
          // Map filePath to fileName for UI compatibility
          fileName: result.filePath ? getFileName(result.filePath) : result.fileName || 'Unknown',
          // Ensure fileExtension is present
          fileExtension: result.fileExtension || (result.filePath ? getFileExtension(result.filePath) : '.package'),
          // Ensure fileSize is present
          fileSize: result.fileSize || 0,
          // Ensure all required properties for the UI
          resourceIntelligenceData: result.intelligence?.resourceIntelligence,
          dependencyData: result.intelligence?.dependencies,
          qualityAssessmentData: result.intelligence?.qualityAssessment,
          metadataConfidence: result.metadataConfidence || 0,
          processingTime: result.processingTime || 0,
          resourceCount: result.resourceCount || 0,
          // Add quality score from intelligence analysis
          qualityScore: result.intelligence?.qualityAssessment?.overallScore || 0,
          // Ensure metadata properties exist with safe fallbacks
          author: result.metadata?.author || result.author || 'Unknown',
          version: result.metadata?.version || result.version || '1.0',
          modName: result.metadata?.modName || result.modName || (result.fileName || 'Unknown Mod'),

          // Add basic category for UI display
          category: result.category || (result.fileExtension === '.ts4script' ? 'script' : 'package'),
          subcategory: result.subcategory || null,

          // Enhanced Content Analysis Fields
          casContent: result.intelligence?.resourceIntelligence?.contentAnalysis?.casContent || null,
          objectContent: result.intelligence?.resourceIntelligence?.contentAnalysis?.objectContent || null,
          objectClassification: result.intelligence?.resourceIntelligence?.contentAnalysis?.objectClassification || null,
          universalClassification: result.intelligence?.resourceIntelligence?.contentAnalysis?.universalClassification || null,

          // StringTable Analysis Fields
          stringTableData: result.intelligence?.stringTableData || null,
          actualModName: result.intelligence?.stringTableData?.modName || result.metadata?.modName || result.modName || null,
          actualDescription: result.intelligence?.stringTableData?.description || result.metadata?.description || null,
          extractedItemNames: result.intelligence?.stringTableData?.itemNames || [],
          hasStringTable: !!(result.intelligence?.stringTableData?.customStringCount > 0),

          // Enhanced metadata fields for better fallbacks (already set above)

          // 🎨 THUMBNAIL DATA MAPPING - Map thumbnail data from analysis results
          thumbnails: result.thumbnails || [],
          primaryThumbnail: result.primaryThumbnail || null,
          thumbnailVariations: result.thumbnailVariations || [],
          hasMultipleVariations: result.hasMultipleVariations || false,
          thumbnailUrl: result.thumbnailUrl || result.primaryThumbnail?.imageData || null,
          thumbnailData: result.primaryThumbnail?.imageData || null, // Legacy support
        }));

        analysisResults.value = processedResults;

        // Force Vue to re-render
        await nextTick();

        // Show warning if there were partial errors but still got some data
        if (!analysisResult.success && analysisResults.value.length > 0) {
          analysisError.value = `Analysis completed with some errors. Showing ${analysisResults.value.length} successfully analyzed mods. Check console for details.`;
        }
      } else {
        analysisError.value = 'No valid mod data found. Check that the folder contains .package or .ts4script files.';
      }
    } else {
      analysisError.value = analysisResult.error || 'Analysis failed. Check console for details.';
    }
  } catch (error) {
    analysisError.value = error instanceof Error ? error.message : 'Failed to analyze mods folder';
  } finally {
    isAnalyzing.value = false;

    // Force another nextTick to ensure UI updates
    await nextTick();
  }
}

async function exportResults() {
  if (analysisResults.value.length === 0) return;

  if (!window.electronAPI) {
    analysisError.value = 'Electron API not available. Please run in Electron app.';
    return;
  }

  try {
    // For now, export as JSON - could add format selection dialog
    const exportResult = await window.electronAPI.exportResults(analysisResults.value, 'json');

    if (exportResult.success) {
      // Could show a success notification here
    } else {
      analysisError.value = exportResult.error;
    }
  } catch (error) {
    analysisError.value = error instanceof Error ? error.message : 'Failed to export results';
  }
}

// Helper function to get filename from path (replaces path.basename)
function getFileName(filePath: string): string {
  if (!filePath) return 'Unknown';
  return filePath.split(/[\\/]/).pop() || 'Unknown';
}

// Helper function to get file extension from path
function getFileExtension(filePath: string): string {
  if (!filePath) return '.package';
  const fileName = filePath.split(/[\\/]/).pop() || '';
  const lastDot = fileName.lastIndexOf('.');
  return lastDot !== -1 ? fileName.substring(lastDot) : '.package';
}



// Auto-load default mods folder on startup
onMounted(async () => {
  // No auto-analysis - user must manually select and analyze folder
});

// Set up the analysis result handler (legacy support)
if (window.electronAPI?.onAnalysisResult) {
  window.electronAPI.onAnalysisResult((result: any) => {
    // This will be handled by the promise in handleAnalyzeRequested
  });
}
</script>

<style>
/* Import our enhanced design system */
@import './styles/simonitor-design-system.css';

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg,
    #4A1A3D 0%,
    #2D1B3D 25%,
    #1A1B3A 50%,
    #2D1B3D 75%,
    #4A1A3D 100%);
  font-family: var(--font-family-sans);
}

.app-header {
  background: linear-gradient(135deg,
    var(--bg-elevated) 0%,
    color-mix(in srgb, var(--primary-green) 8%, var(--bg-elevated)) 100%);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
}

.app-title h1 {
  background: linear-gradient(135deg,
    var(--primary-green) 0%,
    var(--primary-orange) 50%,
    var(--primary-red-orange) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: var(--font-bold);
}

.app-main {
  flex: 1;
}

.app-footer {
  background-color: var(--bg-primary);
  border-top: 1px solid var(--border-light);
  margin-top: auto;
}

/* ===== MODERN BUTTON STYLES ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  transition: all var(--duration-200) var(--ease-out);
  border: 1px solid transparent;
  cursor: pointer;
  text-decoration: none;
  box-shadow: var(--button-shadow);
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--button-shadow-hover);
}

.btn-primary {
  background: var(--color-primary);
  color: var(--text-on-primary);
  border-color: var(--color-primary);
  box-shadow: 0 2px 4px color-mix(in srgb, var(--color-primary) 25%, transparent);
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
  box-shadow: 0 4px 12px color-mix(in srgb, var(--color-primary) 30%, transparent);
}

.btn-primary:active:not(:disabled) {
  background: var(--color-primary-active);
  border-color: var(--color-primary-active);
}

.btn-secondary {
  background: var(--color-secondary-light);
  color: var(--color-secondary);
  border-color: var(--color-secondary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-secondary);
  color: var(--text-on-secondary);
  border-color: var(--color-secondary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px color-mix(in srgb, var(--color-secondary) 25%, transparent);
}

.btn-sm {
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
}

/* Badge styles */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
}

.badge-info {
  background: var(--info-bg);
  color: var(--info);
  border: 1px solid var(--info-border);
}

/* Card styles */
.card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

/* Container */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

/* Utility classes */
.w-4 { width: 16px; }
.h-4 { height: 16px; }
.w-6 { width: 24px; }
.h-6 { height: 24px; }

.text-error {
  color: var(--error);
}

.text-muted {
  color: var(--text-secondary);
}

.welcome-content {
  max-width: 600px;
  margin: 0 auto;
}

.welcome-icon {
  color: var(--text-muted);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.feature-item {
  text-align: center;
  padding: var(--spacing-lg);
}

.feature-icon {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.feature-item h4 {
  margin: 0 0 var(--spacing-sm) 0;
}

.feature-item p {
  margin: 0;
}

.max-w-md {
  max-width: 28rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.text-error {
  color: var(--error-color);
}

.loading-content .loading {
  border-width: 3px;
}

@media (max-width: 768px) {
  .app-title h1 {
    font-size: 1.5rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .feature-item {
    padding: var(--spacing-md);
  }
}
</style>