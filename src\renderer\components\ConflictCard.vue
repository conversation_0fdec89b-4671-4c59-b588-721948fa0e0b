<template>
  <div class="conflict-card" :class="severityClass">
    <!-- Card Header -->
    <div class="card-header">
      <div class="header-left">
        <div class="severity-indicator">
          <component :is="severityIcon" class="w-4 h-4" />
        </div>
        <div class="conflict-info">
          <h4 class="conflict-title">{{ conflict.title }}</h4>
          <p class="conflict-type">{{ formatConflictType(conflict.type) }}</p>
        </div>
      </div>
      
      <div class="header-right">
        <span class="severity-badge" :class="severityClass">
          {{ conflict.severity.toUpperCase() }}
        </span>
        <div class="confidence-indicator" :title="`Confidence: ${Math.round(conflict.confidence * 100)}%`">
          <div class="confidence-bar">
            <div 
              class="confidence-fill" 
              :style="{ width: `${conflict.confidence * 100}%` }"
              :class="confidenceClass"
            ></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Card Content -->
    <div class="card-content">
      <p class="conflict-description">{{ conflict.description }}</p>
      
      <!-- Affected Mods -->
      <div v-if="conflict.affectedMods.length > 0" class="affected-mods">
        <h5 class="affected-title">Affected Mods:</h5>
        <div class="mod-list">
          <span
            v-for="mod in displayedMods"
            :key="mod"
            class="mod-tag"
            :title="mod"
          >
            {{ getModDisplayName(mod) }}
          </span>
          <button
            v-if="conflict.affectedMods.length > maxDisplayedMods"
            @click="showAllMods = !showAllMods"
            class="show-more-btn"
          >
            {{ showAllMods ? 'Show Less' : `+${conflict.affectedMods.length - maxDisplayedMods} more` }}
          </button>
        </div>
      </div>
      
      <!-- Resolution Strategy -->
      <div class="resolution-section">
        <h5 class="resolution-title">
          <WrenchScrewdriverIcon class="w-4 h-4" />
          Resolution Strategy
        </h5>
        <p class="resolution-description">{{ conflict.resolution.description }}</p>
        
        <!-- Resolution Steps -->
        <div v-if="conflict.resolution.steps.length > 0" class="resolution-steps">
          <button
            @click="showSteps = !showSteps"
            class="steps-toggle"
          >
            <ChevronRightIcon 
              class="w-4 h-4 transition-transform"
              :class="{ 'rotate-90': showSteps }"
            />
            {{ showSteps ? 'Hide' : 'Show' }} Steps ({{ conflict.resolution.steps.length }})
          </button>
          
          <ol v-if="showSteps" class="steps-list">
            <li v-for="(step, index) in conflict.resolution.steps" :key="index" class="step-item">
              {{ step }}
            </li>
          </ol>
        </div>
      </div>
      
      <!-- User Resolution Status -->
      <div v-if="conflict.userResolution" class="user-resolution">
        <div class="resolution-status" :class="resolutionStatusClass">
          <component :is="resolutionStatusIcon" class="w-4 h-4" />
          <span>{{ formatUserResolution(conflict.userResolution) }}</span>
        </div>
      </div>
    </div>
    
    <!-- Card Actions -->
    <div class="card-actions">
      <button
        v-if="!conflict.userResolution"
        @click="$emit('resolve', conflict)"
        class="action-btn resolve-btn"
        :disabled="!conflict.autoFixAvailable"
      >
        <CheckIcon class="w-4 h-4" />
        {{ conflict.autoFixAvailable ? 'Auto Resolve' : 'Mark Resolved' }}
      </button>
      
      <button
        v-if="!conflict.userResolution"
        @click="$emit('ignore', conflict)"
        class="action-btn ignore-btn"
      >
        <EyeSlashIcon class="w-4 h-4" />
        Ignore
      </button>
      
      <button
        @click="$emit('showDetails', conflict)"
        class="action-btn details-btn"
      >
        <InformationCircleIcon class="w-4 h-4" />
        Details
      </button>
      
      <!-- Undo button for resolved/ignored conflicts -->
      <button
        v-if="conflict.userResolution"
        @click="$emit('resolve', { ...conflict, userResolution: undefined })"
        class="action-btn undo-btn"
      >
        <ArrowUturnLeftIcon class="w-4 h-4" />
        Undo
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  ExclamationTriangleIcon,
  XCircleIcon,
  ExclamationCircleIcon,
  InformationCircleIcon,
  CheckIcon,
  EyeSlashIcon,
  WrenchScrewdriverIcon,
  ChevronRightIcon,
  ArrowUturnLeftIcon,
  CheckCircleIcon,
  MinusCircleIcon
} from '@heroicons/vue/24/outline';

import type { UnifiedConflict } from '../../services/analysis/conflicts/UnifiedConflictDetector';

// Props
interface Props {
  conflict: UnifiedConflict;
}

const props = defineProps<Props>();

// Emits
defineEmits<{
  resolve: [conflict: UnifiedConflict];
  ignore: [conflict: UnifiedConflict];
  showDetails: [conflict: UnifiedConflict];
}>();

// Reactive state
const showAllMods = ref(false);
const showSteps = ref(false);
const maxDisplayedMods = 3;

// Computed properties
const severityClass = computed(() => `severity-${props.conflict.severity}`);

const severityIcon = computed(() => {
  switch (props.conflict.severity) {
    case 'critical': return XCircleIcon;
    case 'high': return ExclamationTriangleIcon;
    case 'medium': return ExclamationCircleIcon;
    case 'low': return InformationCircleIcon;
    default: return InformationCircleIcon;
  }
});

const confidenceClass = computed(() => {
  const confidence = props.conflict.confidence;
  if (confidence >= 0.8) return 'confidence-high';
  if (confidence >= 0.6) return 'confidence-medium';
  return 'confidence-low';
});

const displayedMods = computed(() => {
  if (showAllMods.value) {
    return props.conflict.affectedMods;
  }
  return props.conflict.affectedMods.slice(0, maxDisplayedMods);
});

const resolutionStatusClass = computed(() => {
  switch (props.conflict.userResolution) {
    case 'resolved': return 'status-resolved';
    case 'ignored': return 'status-ignored';
    default: return '';
  }
});

const resolutionStatusIcon = computed(() => {
  switch (props.conflict.userResolution) {
    case 'resolved': return CheckCircleIcon;
    case 'ignored': return MinusCircleIcon;
    default: return InformationCircleIcon;
  }
});

// Methods
const formatConflictType = (type: string): string => {
  return type
    .replace(/_/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
};

const getModDisplayName = (modFileName: string): string => {
  return modFileName
    .replace(/\.(package|ts4script)$/i, '')
    .replace(/[_-]/g, ' ')
    .substring(0, 20) + (modFileName.length > 20 ? '...' : '');
};

const formatUserResolution = (resolution: string): string => {
  switch (resolution) {
    case 'resolved': return 'Marked as Resolved';
    case 'ignored': return 'Ignored';
    default: return resolution;
  }
};
</script>

<style scoped>
.conflict-card {
  @apply bg-surface border rounded-lg overflow-hidden transition-all duration-200;
}

.conflict-card:hover {
  @apply shadow-md;
}

.conflict-card.severity-critical {
  @apply border-error/30 bg-error/5;
}

.conflict-card.severity-high {
  @apply border-warning/30 bg-warning/5;
}

.conflict-card.severity-medium {
  @apply border-info/30 bg-info/5;
}

.conflict-card.severity-low {
  @apply border-success/30 bg-success/5;
}

.card-header {
  @apply flex items-center justify-between p-4 border-b bg-background/50;
}

.header-left {
  @apply flex items-center gap-3;
}

.severity-indicator {
  @apply flex-shrink-0;
}

.severity-critical .severity-indicator {
  @apply text-error;
}

.severity-high .severity-indicator {
  @apply text-warning;
}

.severity-medium .severity-indicator {
  @apply text-info;
}

.severity-low .severity-indicator {
  @apply text-success;
}

.conflict-title {
  @apply font-medium text-sm;
}

.conflict-type {
  @apply text-xs text-muted;
}

.header-right {
  @apply flex items-center gap-3;
}

.severity-badge {
  @apply px-2 py-1 text-xs font-medium rounded;
}

.severity-badge.severity-critical {
  @apply bg-error text-error-foreground;
}

.severity-badge.severity-high {
  @apply bg-warning text-warning-foreground;
}

.severity-badge.severity-medium {
  @apply bg-info text-info-foreground;
}

.severity-badge.severity-low {
  @apply bg-success text-success-foreground;
}

.confidence-indicator {
  @apply flex items-center gap-1;
}

.confidence-bar {
  @apply w-12 h-2 bg-surface rounded-full overflow-hidden;
}

.confidence-fill {
  @apply h-full transition-all duration-300;
}

.confidence-high {
  @apply bg-success;
}

.confidence-medium {
  @apply bg-warning;
}

.confidence-low {
  @apply bg-error;
}

.card-content {
  @apply p-4 space-y-4;
}

.conflict-description {
  @apply text-sm text-muted;
}

.affected-mods {
  @apply space-y-2;
}

.affected-title {
  @apply text-sm font-medium;
}

.mod-list {
  @apply flex flex-wrap gap-2;
}

.mod-tag {
  @apply px-2 py-1 bg-background border rounded text-xs;
}

.show-more-btn {
  @apply px-2 py-1 text-xs text-primary hover:bg-primary/10 rounded transition-colors;
}

.resolution-section {
  @apply space-y-2;
}

.resolution-title {
  @apply flex items-center gap-2 text-sm font-medium;
}

.resolution-description {
  @apply text-sm text-muted;
}

.steps-toggle {
  @apply flex items-center gap-1 text-sm text-primary hover:text-primary/80 transition-colors;
}

.steps-list {
  @apply mt-2 space-y-1 text-sm text-muted;
}

.step-item {
  @apply flex items-start gap-2;
}

.step-item::before {
  content: counter(list-item);
  @apply flex-shrink-0 w-5 h-5 bg-primary/10 text-primary text-xs rounded-full flex items-center justify-center;
}

.user-resolution {
  @apply pt-2 border-t;
}

.resolution-status {
  @apply flex items-center gap-2 text-sm;
}

.status-resolved {
  @apply text-success;
}

.status-ignored {
  @apply text-muted;
}

.card-actions {
  @apply flex items-center gap-2 p-4 border-t bg-background/30;
}

.action-btn {
  @apply flex items-center gap-1 px-3 py-2 text-sm border rounded transition-colors;
}

.resolve-btn {
  @apply border-success text-success hover:bg-success hover:text-success-foreground;
}

.ignore-btn {
  @apply border-muted text-muted hover:bg-muted hover:text-muted-foreground;
}

.details-btn {
  @apply border-info text-info hover:bg-info hover:text-info-foreground;
}

.undo-btn {
  @apply border-warning text-warning hover:bg-warning hover:text-warning-foreground;
}

.action-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .card-header {
    @apply flex-col gap-3;
  }
  
  .header-right {
    @apply w-full justify-between;
  }
  
  .card-actions {
    @apply flex-col gap-2;
  }
  
  .action-btn {
    @apply w-full justify-center;
  }
}
</style>
