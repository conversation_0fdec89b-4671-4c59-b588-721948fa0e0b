// Test script to verify dashboard components are working
// Run this in the browser console to test component functionality

console.log('🧪 Testing Dashboard Components...');

// Test 1: Check if components are loaded
const checkComponents = () => {
  const dashboard = document.querySelector('.mod-dashboard');
  const header = document.querySelector('.dashboard-header');
  const search = document.querySelector('.dashboard-search');
  const filters = document.querySelector('.dashboard-filters');
  const stats = document.querySelector('.dashboard-stats');
  const grid = document.querySelector('.mod-grid');
  
  console.log('📊 Component Check Results:');
  console.log('- Dashboard:', dashboard ? '✅ Found' : '❌ Missing');
  console.log('- Header:', header ? '✅ Found' : '❌ Missing');
  console.log('- Search:', search ? '✅ Found' : '❌ Missing');
  console.log('- Filters:', filters ? '✅ Found' : '❌ Missing');
  console.log('- Stats:', stats ? '✅ Found' : '❌ Missing');
  console.log('- Grid:', grid ? '✅ Found' : '❌ Missing');
  
  return {
    dashboard: !!dashboard,
    header: !!header,
    search: !!search,
    filters: !!filters,
    stats: !!stats,
    grid: !!grid
  };
};

// Test 2: Check CSS imports
const checkStyles = () => {
  const computedStyle = getComputedStyle(document.querySelector('.mod-dashboard') || document.body);
  const background = computedStyle.background || computedStyle.backgroundColor;
  
  console.log('🎨 Style Check Results:');
  console.log('- Background:', background);
  console.log('- Has gradient:', background.includes('gradient') ? '✅ Yes' : '❌ No');
  
  return {
    hasBackground: !!background,
    hasGradient: background.includes('gradient')
  };
};

// Test 3: Check for mod data
const checkModData = () => {
  const modCards = document.querySelectorAll('.mod-thumbnail-card');
  const thumbnails = document.querySelectorAll('.thumbnail-image');
  const placeholders = document.querySelectorAll('.thumbnail-placeholder');
  
  console.log('📦 Mod Data Check Results:');
  console.log('- Mod cards:', modCards.length);
  console.log('- Thumbnails:', thumbnails.length);
  console.log('- Placeholders:', placeholders.length);
  
  return {
    modCards: modCards.length,
    thumbnails: thumbnails.length,
    placeholders: placeholders.length
  };
};

// Run all tests
const runTests = () => {
  console.log('🚀 Starting Dashboard Component Tests...');
  
  const componentResults = checkComponents();
  const styleResults = checkStyles();
  const dataResults = checkModData();
  
  console.log('📋 Test Summary:');
  console.log('- Components loaded:', Object.values(componentResults).filter(Boolean).length + '/6');
  console.log('- Styles working:', styleResults.hasGradient ? '✅ Yes' : '❌ No');
  console.log('- Mod data present:', dataResults.modCards > 0 ? '✅ Yes' : '❌ No');
  
  return {
    components: componentResults,
    styles: styleResults,
    data: dataResults
  };
};

// Auto-run tests
setTimeout(runTests, 1000);

// Export for manual testing
window.dashboardTests = {
  checkComponents,
  checkStyles,
  checkModData,
  runTests
};

console.log('✅ Test script loaded. Run window.dashboardTests.runTests() to test manually.');
