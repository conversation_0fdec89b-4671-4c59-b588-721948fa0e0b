/**
 * Progressive Loading Composable for Vue 3
 * 
 * Implements intersection observer for smart image loading and progressive content loading
 * Optimized for large collections with performance monitoring
 */

import { ref, onMounted, onUnmounted, nextTick } from 'vue';

interface ProgressiveLoadingOptions {
  rootMargin?: string;
  threshold?: number | number[];
  loadingDelay?: number;
  batchSize?: number;
  enablePrefetch?: boolean;
  prefetchDistance?: number;
}

interface LoadingItem {
  element: Element;
  index: number;
  isLoaded: boolean;
  isLoading: boolean;
  priority: number;
}

export function useProgressiveLoading(options: ProgressiveLoadingOptions = {}) {
  const {
    rootMargin = '50px',
    threshold = 0.1,
    loadingDelay = 100,
    batchSize = 5,
    enablePrefetch = true,
    prefetchDistance = 200
  } = options;

  // State
  const isObserving = ref(false);
  const loadedItems = ref(new Set<number>());
  const loadingItems = ref(new Set<number>());
  const visibleItems = ref(new Set<number>());
  
  // Performance tracking
  const loadingStats = ref({
    totalItems: 0,
    loadedCount: 0,
    loadingTime: 0,
    averageLoadTime: 0
  });

  // Internal state
  let observer: IntersectionObserver | null = null;
  let loadingQueue: LoadingItem[] = [];
  let isProcessingQueue = false;
  const loadingTimes = new Map<number, number>();

  /**
   * Initialize the intersection observer
   */
  const initializeObserver = () => {
    if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
      console.warn('IntersectionObserver not supported');
      return;
    }

    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const element = entry.target;
          const index = parseInt(element.getAttribute('data-index') || '0', 10);
          
          if (entry.isIntersecting) {
            visibleItems.value.add(index);
            queueForLoading(element, index);
            
            // Prefetch nearby items if enabled
            if (enablePrefetch) {
              prefetchNearbyItems(index);
            }
          } else {
            visibleItems.value.delete(index);
          }
        });
        
        processLoadingQueue();
      },
      {
        rootMargin,
        threshold
      }
    );

    isObserving.value = true;
  };

  /**
   * Queue an item for loading with priority
   */
  const queueForLoading = (element: Element, index: number) => {
    if (loadedItems.value.has(index) || loadingItems.value.has(index)) {
      return;
    }

    const priority = calculatePriority(element, index);
    const loadingItem: LoadingItem = {
      element,
      index,
      isLoaded: false,
      isLoading: false,
      priority
    };

    // Insert in priority order
    const insertIndex = loadingQueue.findIndex(item => item.priority < priority);
    if (insertIndex === -1) {
      loadingQueue.push(loadingItem);
    } else {
      loadingQueue.splice(insertIndex, 0, loadingItem);
    }
  };

  /**
   * Calculate loading priority based on visibility and position
   */
  const calculatePriority = (element: Element, index: number): number => {
    const rect = element.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;
    
    // Base priority on distance from viewport center
    const centerY = viewportHeight / 2;
    const centerX = viewportWidth / 2;
    const elementCenterY = rect.top + rect.height / 2;
    const elementCenterX = rect.left + rect.width / 2;
    
    const distanceFromCenter = Math.sqrt(
      Math.pow(elementCenterX - centerX, 2) + 
      Math.pow(elementCenterY - centerY, 2)
    );
    
    // Higher priority for items closer to center and currently visible
    let priority = 1000 - distanceFromCenter;
    
    // Boost priority for visible items
    if (visibleItems.value.has(index)) {
      priority += 500;
    }
    
    // Boost priority for items in viewport
    if (rect.top >= 0 && rect.bottom <= viewportHeight) {
      priority += 200;
    }
    
    return Math.max(0, priority);
  };

  /**
   * Process the loading queue in batches
   */
  const processLoadingQueue = async () => {
    if (isProcessingQueue || loadingQueue.length === 0) {
      return;
    }

    isProcessingQueue = true;

    try {
      // Process items in batches to avoid overwhelming the browser
      const batch = loadingQueue.splice(0, batchSize);
      
      const loadPromises = batch.map(async (item) => {
        if (loadingItems.value.has(item.index)) {
          return;
        }

        loadingItems.value.add(item.index);
        const startTime = performance.now();
        
        try {
          await loadItem(item);
          
          const loadTime = performance.now() - startTime;
          loadingTimes.set(item.index, loadTime);
          loadedItems.value.add(item.index);
          
          // Update stats
          loadingStats.value.loadedCount++;
          loadingStats.value.loadingTime += loadTime;
          loadingStats.value.averageLoadTime = 
            loadingStats.value.loadingTime / loadingStats.value.loadedCount;
          
        } catch (error) {
          console.warn(`Failed to load item ${item.index}:`, error);
        } finally {
          loadingItems.value.delete(item.index);
        }
      });

      await Promise.all(loadPromises);

      // Add delay between batches to prevent blocking
      if (loadingQueue.length > 0) {
        setTimeout(() => {
          isProcessingQueue = false;
          processLoadingQueue();
        }, loadingDelay);
      } else {
        isProcessingQueue = false;
      }

    } catch (error) {
      console.error('Error processing loading queue:', error);
      isProcessingQueue = false;
    }
  };

  /**
   * Load an individual item
   */
  const loadItem = async (item: LoadingItem): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = item.element.querySelector('img[data-src]') as HTMLImageElement;
      
      if (!img) {
        resolve();
        return;
      }

      const src = img.getAttribute('data-src');
      if (!src) {
        resolve();
        return;
      }

      const tempImg = new Image();
      
      tempImg.onload = () => {
        img.src = src;
        img.removeAttribute('data-src');
        img.classList.add('loaded');
        resolve();
      };
      
      tempImg.onerror = () => {
        img.classList.add('error');
        reject(new Error(`Failed to load image: ${src}`));
      };
      
      tempImg.src = src;
    });
  };

  /**
   * Prefetch nearby items for smoother scrolling
   */
  const prefetchNearbyItems = (currentIndex: number) => {
    if (!enablePrefetch) return;

    const prefetchIndices = [];
    const prefetchCount = Math.floor(prefetchDistance / 100); // Rough estimate
    
    for (let i = 1; i <= prefetchCount; i++) {
      const nextIndex = currentIndex + i;
      const prevIndex = currentIndex - i;
      
      if (nextIndex < loadingStats.value.totalItems) {
        prefetchIndices.push(nextIndex);
      }
      if (prevIndex >= 0) {
        prefetchIndices.push(prevIndex);
      }
    }

    // Queue prefetch items with lower priority
    prefetchIndices.forEach(index => {
      const element = document.querySelector(`[data-index="${index}"]`);
      if (element && !loadedItems.value.has(index) && !loadingItems.value.has(index)) {
        queueForLoading(element, index);
      }
    });
  };

  /**
   * Observe an element for progressive loading
   */
  const observe = (element: Element) => {
    if (observer && element) {
      observer.observe(element);
    }
  };

  /**
   * Stop observing an element
   */
  const unobserve = (element: Element) => {
    if (observer && element) {
      observer.unobserve(element);
    }
  };

  /**
   * Set the total number of items for stats tracking
   */
  const setTotalItems = (total: number) => {
    loadingStats.value.totalItems = total;
  };

  /**
   * Reset all loading state
   */
  const reset = () => {
    loadedItems.value.clear();
    loadingItems.value.clear();
    visibleItems.value.clear();
    loadingQueue = [];
    isProcessingQueue = false;
    loadingTimes.clear();
    
    loadingStats.value = {
      totalItems: 0,
      loadedCount: 0,
      loadingTime: 0,
      averageLoadTime: 0
    };
  };

  /**
   * Get loading statistics
   */
  const getStats = () => {
    return {
      ...loadingStats.value,
      loadingProgress: loadingStats.value.totalItems > 0 
        ? (loadingStats.value.loadedCount / loadingStats.value.totalItems) * 100 
        : 0,
      queueSize: loadingQueue.length,
      isProcessing: isProcessingQueue
    };
  };

  // Lifecycle
  onMounted(() => {
    nextTick(() => {
      initializeObserver();
    });
  });

  onUnmounted(() => {
    if (observer) {
      observer.disconnect();
      observer = null;
    }
    isObserving.value = false;
  });

  return {
    // State
    isObserving,
    loadedItems,
    loadingItems,
    visibleItems,
    loadingStats,
    
    // Methods
    observe,
    unobserve,
    setTotalItems,
    reset,
    getStats,
    
    // Computed
    isLoaded: (index: number) => loadedItems.value.has(index),
    isLoading: (index: number) => loadingItems.value.has(index),
    isVisible: (index: number) => visibleItems.value.has(index)
  };
}
