/**
 * Test Broken CC Detection System
 * 
 * Tests the comprehensive broken CC detection and safe deletion system including:
 * - Detection of corrupted packages, missing textures, broken meshes
 * - Safe deletion with backup/restore capabilities
 * - Performance with large mod collections
 * - Integration with existing analysis system
 * 
 * Addresses Reddit request: "broken CC/texture clash detection with auto-deletion"
 */

import * as fs from 'fs';
import * as path from 'path';
import { BrokenCCDetectionService, BrokenCCType, BrokenCCSeverity } from '../services/analysis/health/BrokenCCDetectionService';
import { SafeDeletionService } from '../services/analysis/health/SafeDeletionService';

interface TestResults {
    totalFiles: number;
    healthyFiles: number;
    brokenFiles: number;
    criticalIssues: number;
    autoDeleteCandidates: number;
    processingTime: number;
    averageTimePerFile: number;
    detectionAccuracy: number;
    safetyTestsPassed: number;
    safetyTestsFailed: number;
    backupTestsPassed: boolean;
    restoreTestsPassed: boolean;
    issueBreakdown: Map<BrokenCCType, number>;
    errors: string[];
}

/**
 * Main test function
 */
async function testBrokenCCDetection(): Promise<void> {
    console.log('🔍 Testing Broken CC Detection System');
    console.log('=' .repeat(60));

    const testResults: TestResults = {
        totalFiles: 0,
        healthyFiles: 0,
        brokenFiles: 0,
        criticalIssues: 0,
        autoDeleteCandidates: 0,
        processingTime: 0,
        averageTimePerFile: 0,
        detectionAccuracy: 0,
        safetyTestsPassed: 0,
        safetyTestsFailed: 0,
        backupTestsPassed: false,
        restoreTestsPassed: false,
        issueBreakdown: new Map(),
        errors: []
    };

    try {
        // Test with real mods
        const modsDirectory = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
        
        if (fs.existsSync(modsDirectory)) {
            await testWithRealMods(modsDirectory, testResults);
        } else {
            console.log('❌ Mods directory not found. Testing with synthetic data...');
            await testWithSyntheticData(testResults);
        }

        // Test safe deletion system
        await testSafeDeletionSystem(testResults);

        // Display results
        displayTestResults(testResults);

    } catch (error) {
        console.error('❌ Test failed:', error);
        testResults.errors.push(error.message);
    }
}

/**
 * Test with real mod files
 */
async function testWithRealMods(modsDirectory: string, results: TestResults): Promise<void> {
    console.log(`📁 Testing with real mods from: ${modsDirectory}`);
    
    const startTime = performance.now();
    
    // Find mod files (limit for testing)
    const modFiles = findModFiles(modsDirectory).slice(0, 30);
    results.totalFiles = modFiles.length;
    
    console.log(`📊 Found ${modFiles.length} mod files for testing`);
    
    // Prepare mod data for analysis
    const modData = [];
    for (const filePath of modFiles) {
        try {
            const buffer = fs.readFileSync(filePath);
            const fileName = path.basename(filePath);
            modData.push({ buffer, fileName, filePath });
        } catch (error) {
            console.log(`❌ Failed to read: ${path.basename(filePath)}`);
            results.errors.push(`Failed to read ${path.basename(filePath)}: ${error.message}`);
        }
    }

    // Run batch analysis
    const batchResult = await BrokenCCDetectionService.analyzeBatch(modData, {
        enableDeepScan: true,
        checkTextureIntegrity: true,
        validateMeshReferences: true,
        detectTextureClashes: true,
        performanceMode: false,
        autoDeleteThreshold: BrokenCCSeverity.HIGH
    });

    // Process results
    results.healthyFiles = batchResult.healthyFiles;
    results.brokenFiles = batchResult.brokenFiles;
    results.autoDeleteCandidates = batchResult.autoDeleteCandidates;
    results.processingTime = batchResult.processingTime;
    results.averageTimePerFile = results.processingTime / results.totalFiles;
    results.issueBreakdown = batchResult.detectionStats;

    // Count critical issues
    results.criticalIssues = batchResult.results.filter(r => 
        r.overallSeverity === BrokenCCSeverity.CRITICAL
    ).length;

    // Log findings
    console.log(`\n📊 Analysis Results:`);
    console.log(`   Healthy Files: ${results.healthyFiles}`);
    console.log(`   Broken Files: ${results.brokenFiles}`);
    console.log(`   Critical Issues: ${results.criticalIssues}`);
    console.log(`   Auto-Delete Candidates: ${results.autoDeleteCandidates}`);

    // Show issue breakdown
    if (results.issueBreakdown.size > 0) {
        console.log(`\n🔍 Issue Breakdown:`);
        for (const [type, count] of results.issueBreakdown) {
            console.log(`   ${type}: ${count}`);
        }
    }

    // Test specific broken files if found
    const brokenResults = batchResult.results.filter(r => !r.isHealthy);
    if (brokenResults.length > 0) {
        console.log(`\n🚨 Broken Files Detected:`);
        brokenResults.slice(0, 5).forEach(result => {
            console.log(`   ${result.fileName}:`);
            result.issues.forEach(issue => {
                console.log(`     - ${issue.type}: ${issue.description} (${issue.severity})`);
            });
        });
    }
}

/**
 * Test with synthetic broken data
 */
async function testWithSyntheticData(results: TestResults): Promise<void> {
    console.log('🧪 Testing with synthetic broken CC data...');
    
    const syntheticTests = [
        {
            name: 'empty_file.package',
            buffer: Buffer.alloc(0),
            expectedIssues: [BrokenCCType.INCOMPLETE_DOWNLOAD]
        },
        {
            name: 'tiny_file.package',
            buffer: Buffer.alloc(100),
            expectedIssues: [BrokenCCType.INCOMPLETE_DOWNLOAD]
        },
        {
            name: 'corrupted_header.package',
            buffer: Buffer.concat([Buffer.from('CORRUPT'), Buffer.alloc(1000)]),
            expectedIssues: [BrokenCCType.CORRUPTED_PACKAGE]
        },
        {
            name: 'null_bytes.package',
            buffer: Buffer.alloc(2000, 0),
            expectedIssues: [BrokenCCType.CORRUPTED_PACKAGE]
        },
        {
            name: 'script_syntax_error.ts4script',
            buffer: Buffer.from('def broken_function(\n    SyntaxError: invalid syntax'),
            expectedIssues: [BrokenCCType.MALFORMED_XML]
        }
    ];

    results.totalFiles = syntheticTests.length;
    let correctDetections = 0;

    for (const test of syntheticTests) {
        try {
            const result = await BrokenCCDetectionService.analyzeMod(
                test.buffer,
                test.name,
                `/synthetic/${test.name}`
            );

            console.log(`\n🧪 Testing ${test.name}:`);
            console.log(`   Healthy: ${result.isHealthy}`);
            console.log(`   Issues: ${result.issues.length}`);
            console.log(`   Severity: ${result.overallSeverity}`);

            // Check if expected issues were detected
            const detectedTypes = result.issues.map(i => i.type);
            const expectedDetected = test.expectedIssues.some(expected => 
                detectedTypes.includes(expected)
            );

            if (expectedDetected) {
                correctDetections++;
                console.log(`   ✅ Correctly detected expected issues`);
            } else {
                console.log(`   ❌ Failed to detect expected issues: ${test.expectedIssues.join(', ')}`);
                console.log(`   Detected: ${detectedTypes.join(', ')}`);
            }

            if (!result.isHealthy) {
                results.brokenFiles++;
                if (result.overallSeverity === BrokenCCSeverity.CRITICAL) {
                    results.criticalIssues++;
                }
                if (result.autoDeleteRecommended) {
                    results.autoDeleteCandidates++;
                }
            } else {
                results.healthyFiles++;
            }

            // Update issue breakdown
            for (const issue of result.issues) {
                results.issueBreakdown.set(
                    issue.type, 
                    (results.issueBreakdown.get(issue.type) || 0) + 1
                );
            }

        } catch (error) {
            console.log(`   ❌ Analysis failed: ${error.message}`);
            results.errors.push(`Synthetic test ${test.name}: ${error.message}`);
        }
    }

    results.detectionAccuracy = (correctDetections / syntheticTests.length) * 100;
    console.log(`\n📊 Detection Accuracy: ${results.detectionAccuracy.toFixed(1)}%`);
}

/**
 * Test safe deletion system
 */
async function testSafeDeletionSystem(results: TestResults): Promise<void> {
    console.log('\n🗑️  Testing Safe Deletion System...');

    try {
        // Create test backup directory
        const testBackupDir = path.join(process.cwd(), 'test-backups');
        
        // Test with synthetic broken file
        const testFile = {
            fileName: 'test_broken.package',
            filePath: path.join(process.cwd(), 'test_broken.package'),
            isHealthy: false,
            issues: [{
                id: 'test-issue',
                type: BrokenCCType.CORRUPTED_PACKAGE,
                severity: BrokenCCSeverity.CRITICAL,
                description: 'Test corrupted package',
                affectedFiles: ['test_broken.package'],
                affectedResources: [],
                autoFixAvailable: false,
                autoDeleteRecommended: true,
                repairSuggestions: [],
                detectionMethod: 'test',
                confidence: 100
            }],
            overallSeverity: BrokenCCSeverity.CRITICAL,
            autoDeleteRecommended: true,
            repairPossible: false,
            analysisTime: 0,
            detectionMethods: ['test']
        };

        // Create test file
        fs.writeFileSync(testFile.filePath, 'test corrupted content');

        // Test safe deletion with backup
        const deletionResult = await SafeDeletionService.safeDelete([testFile], {
            createBackup: true,
            backupLocation: testBackupDir,
            confirmBeforeDelete: false,
            dryRun: false
        });

        console.log(`   Deletion Success: ${deletionResult.success}`);
        console.log(`   Files Deleted: ${deletionResult.deletedFiles.length}`);
        console.log(`   Files Backed Up: ${deletionResult.backedUpFiles.length}`);
        console.log(`   Errors: ${deletionResult.errors.length}`);

        if (deletionResult.success && deletionResult.deletedFiles.length > 0) {
            results.safetyTestsPassed++;
            results.backupTestsPassed = deletionResult.backedUpFiles.length > 0;
            
            // Test restore functionality
            if (deletionResult.operation.backups.length > 0) {
                const backup = deletionResult.operation.backups[0];
                const restored = await SafeDeletionService.restoreFromBackup(backup.id);
                results.restoreTestsPassed = restored;
                
                if (restored) {
                    console.log(`   ✅ Restore test passed`);
                    // Clean up restored file
                    if (fs.existsSync(testFile.filePath)) {
                        fs.unlinkSync(testFile.filePath);
                    }
                } else {
                    console.log(`   ❌ Restore test failed`);
                }
            }
        } else {
            results.safetyTestsFailed++;
            console.log(`   ❌ Safe deletion test failed`);
        }

        // Test backup listing
        const backups = await SafeDeletionService.listBackups(testBackupDir);
        console.log(`   Available Backups: ${backups.length}`);

        // Clean up test backup directory
        if (fs.existsSync(testBackupDir)) {
            fs.rmSync(testBackupDir, { recursive: true, force: true });
        }

    } catch (error) {
        console.error(`❌ Safe deletion test failed:`, error);
        results.safetyTestsFailed++;
        results.errors.push(`Safe deletion test: ${error.message}`);
    }
}

/**
 * Find mod files in directory
 */
function findModFiles(directory: string): string[] {
    const modFiles: string[] = [];
    
    function scanDirectory(dir: string, depth: number = 0): void {
        if (depth > 2) return; // Limit recursion depth
        
        try {
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    scanDirectory(fullPath, depth + 1);
                } else if (item.toLowerCase().endsWith('.package') || item.toLowerCase().endsWith('.ts4script')) {
                    modFiles.push(fullPath);
                }
            }
        } catch (error) {
            console.warn(`Failed to scan directory ${dir}:`, error.message);
        }
    }
    
    scanDirectory(directory);
    return modFiles;
}

/**
 * Display test results
 */
function displayTestResults(results: TestResults): void {
    console.log('\n' + '='.repeat(60));
    console.log('📊 BROKEN CC DETECTION TEST RESULTS');
    console.log('='.repeat(60));
    
    console.log(`📁 Total Files Tested: ${results.totalFiles}`);
    console.log(`✅ Healthy Files: ${results.healthyFiles}`);
    console.log(`🚨 Broken Files: ${results.brokenFiles}`);
    console.log(`💀 Critical Issues: ${results.criticalIssues}`);
    console.log(`🗑️  Auto-Delete Candidates: ${results.autoDeleteCandidates}`);
    
    console.log('\n⏱️  PERFORMANCE METRICS:');
    console.log(`   Total Processing Time: ${Math.round(results.processingTime)}ms`);
    console.log(`   Average Time per File: ${Math.round(results.averageTimePerFile)}ms`);
    
    console.log('\n🎯 DETECTION ACCURACY:');
    if (results.detectionAccuracy > 0) {
        console.log(`   Synthetic Test Accuracy: ${results.detectionAccuracy.toFixed(1)}%`);
    }
    console.log(`   Detection Rate: ${((results.brokenFiles / results.totalFiles) * 100).toFixed(1)}%`);
    
    console.log('\n🛡️  SAFETY TESTS:');
    console.log(`   Safety Tests Passed: ${results.safetyTestsPassed}`);
    console.log(`   Safety Tests Failed: ${results.safetyTestsFailed}`);
    console.log(`   Backup System: ${results.backupTestsPassed ? '✅ Working' : '❌ Failed'}`);
    console.log(`   Restore System: ${results.restoreTestsPassed ? '✅ Working' : '❌ Failed'}`);
    
    if (results.issueBreakdown.size > 0) {
        console.log('\n🔍 ISSUE BREAKDOWN:');
        for (const [type, count] of results.issueBreakdown) {
            console.log(`   ${type}: ${count}`);
        }
    }
    
    if (results.errors.length > 0) {
        console.log('\n❌ ERRORS:');
        results.errors.forEach((error, index) => {
            console.log(`   ${index + 1}. ${error}`);
        });
    }
    
    // Overall assessment
    console.log('\n📈 SYSTEM ASSESSMENT:');
    if (results.averageTimePerFile < 200) {
        console.log('   ✅ Excellent performance (<200ms per file)');
    } else if (results.averageTimePerFile < 500) {
        console.log('   ✅ Good performance (<500ms per file)');
    } else {
        console.log('   ⚠️  Performance needs optimization (>500ms per file)');
    }
    
    if (results.safetyTestsPassed > results.safetyTestsFailed) {
        console.log('   ✅ Safety systems working correctly');
    } else {
        console.log('   ⚠️  Safety systems need attention');
    }
    
    if (results.backupTestsPassed && results.restoreTestsPassed) {
        console.log('   ✅ Backup/restore system fully functional');
    } else {
        console.log('   ⚠️  Backup/restore system needs fixes');
    }
    
    console.log('\n🔍 Broken CC Detection System Test Complete!');
}

// Run the test
if (require.main === module) {
    testBrokenCCDetection().catch(console.error);
}

export { testBrokenCCDetection };
