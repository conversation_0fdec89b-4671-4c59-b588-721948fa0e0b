/**
 * Safe Deletion Service
 * 
 * Provides safe deletion capabilities with backup/restore functionality for broken CC.
 * Implements confirmation dialogs and recovery options.
 * 
 * Addresses Reddit request: "broken CC/texture clash detection with auto-deletion"
 */

import * as fs from 'fs';
import * as path from 'path';
import type { BrokenCCAnalysisResult, BrokenCCIssue } from './BrokenCCDetectionService';

export interface DeletionBackup {
    id: string;
    timestamp: number;
    originalPath: string;
    backupPath: string;
    fileName: string;
    fileSize: number;
    reason: string;
    issues: BrokenCCIssue[];
    canRestore: boolean;
    metadata: {
        deletedBy: string;
        deletionMethod: 'manual' | 'auto' | 'batch';
        backupLocation: string;
    };
}

export interface DeletionOperation {
    id: string;
    files: string[];
    backupLocation: string;
    reason: string;
    timestamp: number;
    status: 'pending' | 'completed' | 'failed' | 'cancelled';
    backups: DeletionBackup[];
    errors: string[];
}

export interface SafeDeletionOptions {
    createBackup?: boolean;
    backupLocation?: string;
    confirmBeforeDelete?: boolean;
    allowBatchDeletion?: boolean;
    maxBackupAge?: number; // days
    maxBackupSize?: number; // MB
    dryRun?: boolean;
}

export interface DeletionResult {
    success: boolean;
    deletedFiles: string[];
    backedUpFiles: string[];
    errors: string[];
    operation: DeletionOperation;
}

/**
 * Service for safely deleting broken CC with backup and restore capabilities
 */
export class SafeDeletionService {
    private static readonly DEFAULT_BACKUP_DIR = path.join(process.cwd(), 'backups', 'deleted-mods');
    private static readonly BACKUP_INDEX_FILE = 'backup-index.json';
    private static backupIndex: Map<string, DeletionBackup> = new Map();

    /**
     * Safely deletes broken CC files with backup
     */
    public static async safeDelete(
        analysisResults: BrokenCCAnalysisResult[],
        options: SafeDeletionOptions = {}
    ): Promise<DeletionResult> {
        const opts = {
            createBackup: true,
            backupLocation: this.DEFAULT_BACKUP_DIR,
            confirmBeforeDelete: true,
            allowBatchDeletion: true,
            maxBackupAge: 30,
            maxBackupSize: 1000,
            dryRun: false,
            ...options
        };

        // Filter files that should be deleted
        const filesToDelete = analysisResults.filter(result => 
            result.autoDeleteRecommended || 
            result.overallSeverity === 'critical'
        );

        if (filesToDelete.length === 0) {
            return {
                success: true,
                deletedFiles: [],
                backedUpFiles: [],
                errors: [],
                operation: this.createEmptyOperation()
            };
        }

        // Create deletion operation
        const operation: DeletionOperation = {
            id: `del-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            files: filesToDelete.map(f => f.filePath),
            backupLocation: opts.backupLocation,
            reason: 'Broken CC auto-deletion',
            timestamp: Date.now(),
            status: 'pending',
            backups: [],
            errors: []
        };

        try {
            // Confirm deletion if required
            if (opts.confirmBeforeDelete && !opts.dryRun) {
                const confirmed = await this.confirmDeletion(filesToDelete);
                if (!confirmed) {
                    operation.status = 'cancelled';
                    return {
                        success: false,
                        deletedFiles: [],
                        backedUpFiles: [],
                        errors: ['Deletion cancelled by user'],
                        operation
                    };
                }
            }

            // Ensure backup directory exists
            if (opts.createBackup) {
                await this.ensureBackupDirectory(opts.backupLocation);
            }

            // Clean old backups if needed
            await this.cleanOldBackups(opts.backupLocation, opts.maxBackupAge, opts.maxBackupSize);

            const deletedFiles: string[] = [];
            const backedUpFiles: string[] = [];
            const errors: string[] = [];

            // Process each file
            for (const result of filesToDelete) {
                try {
                    if (opts.dryRun) {
                        console.log(`[DRY RUN] Would delete: ${result.filePath}`);
                        deletedFiles.push(result.filePath);
                        continue;
                    }

                    // Create backup if enabled
                    let backup: DeletionBackup | null = null;
                    if (opts.createBackup) {
                        backup = await this.createBackup(result, operation, opts.backupLocation);
                        if (backup) {
                            operation.backups.push(backup);
                            backedUpFiles.push(result.filePath);
                        }
                    }

                    // Delete the file
                    await this.deleteFile(result.filePath);
                    deletedFiles.push(result.filePath);

                    console.log(`✅ Deleted broken CC: ${result.fileName}`);

                } catch (error) {
                    const errorMsg = `Failed to delete ${result.fileName}: ${error.message}`;
                    errors.push(errorMsg);
                    operation.errors.push(errorMsg);
                    console.error(`❌ ${errorMsg}`);
                }
            }

            // Update operation status
            operation.status = errors.length === 0 ? 'completed' : 'failed';

            // Save backup index
            if (opts.createBackup && operation.backups.length > 0) {
                await this.saveBackupIndex(opts.backupLocation);
            }

            return {
                success: errors.length === 0,
                deletedFiles,
                backedUpFiles,
                errors,
                operation
            };

        } catch (error) {
            operation.status = 'failed';
            operation.errors.push(error.message);
            
            return {
                success: false,
                deletedFiles: [],
                backedUpFiles: [],
                errors: [error.message],
                operation
            };
        }
    }

    /**
     * Creates a backup of a file before deletion
     */
    private static async createBackup(
        result: BrokenCCAnalysisResult,
        operation: DeletionOperation,
        backupLocation: string
    ): Promise<DeletionBackup | null> {
        try {
            const timestamp = Date.now();
            const backupFileName = `${timestamp}_${result.fileName}`;
            const backupPath = path.join(backupLocation, backupFileName);

            // Copy file to backup location
            await fs.promises.copyFile(result.filePath, backupPath);

            const backup: DeletionBackup = {
                id: `backup-${timestamp}-${Math.random().toString(36).substr(2, 9)}`,
                timestamp,
                originalPath: result.filePath,
                backupPath,
                fileName: result.fileName,
                fileSize: fs.statSync(result.filePath).size,
                reason: `Broken CC: ${result.issues.map(i => i.type).join(', ')}`,
                issues: result.issues,
                canRestore: true,
                metadata: {
                    deletedBy: 'SafeDeletionService',
                    deletionMethod: 'auto',
                    backupLocation
                }
            };

            // Add to backup index
            this.backupIndex.set(backup.id, backup);

            return backup;

        } catch (error) {
            console.error(`Failed to create backup for ${result.fileName}:`, error);
            return null;
        }
    }

    /**
     * Restores a file from backup
     */
    public static async restoreFromBackup(backupId: string): Promise<boolean> {
        const backup = this.backupIndex.get(backupId);
        if (!backup) {
            throw new Error(`Backup not found: ${backupId}`);
        }

        if (!backup.canRestore) {
            throw new Error(`Backup cannot be restored: ${backupId}`);
        }

        try {
            // Check if backup file exists
            if (!fs.existsSync(backup.backupPath)) {
                throw new Error(`Backup file not found: ${backup.backupPath}`);
            }

            // Check if original location is available
            if (fs.existsSync(backup.originalPath)) {
                throw new Error(`File already exists at original location: ${backup.originalPath}`);
            }

            // Restore the file
            await fs.promises.copyFile(backup.backupPath, backup.originalPath);

            console.log(`✅ Restored: ${backup.fileName} to ${backup.originalPath}`);
            return true;

        } catch (error) {
            console.error(`❌ Failed to restore ${backup.fileName}:`, error);
            throw error;
        }
    }

    /**
     * Lists all available backups
     */
    public static async listBackups(backupLocation?: string): Promise<DeletionBackup[]> {
        const location = backupLocation || this.DEFAULT_BACKUP_DIR;
        await this.loadBackupIndex(location);
        return Array.from(this.backupIndex.values()).sort((a, b) => b.timestamp - a.timestamp);
    }

    /**
     * Deletes a backup permanently
     */
    public static async deleteBackup(backupId: string): Promise<boolean> {
        const backup = this.backupIndex.get(backupId);
        if (!backup) {
            throw new Error(`Backup not found: ${backupId}`);
        }

        try {
            // Delete backup file
            if (fs.existsSync(backup.backupPath)) {
                await fs.promises.unlink(backup.backupPath);
            }

            // Remove from index
            this.backupIndex.delete(backupId);

            // Save updated index
            await this.saveBackupIndex(backup.metadata.backupLocation);

            console.log(`✅ Deleted backup: ${backup.fileName}`);
            return true;

        } catch (error) {
            console.error(`❌ Failed to delete backup ${backup.fileName}:`, error);
            throw error;
        }
    }

    /**
     * Cleans old backups based on age and size limits
     */
    private static async cleanOldBackups(
        backupLocation: string,
        maxAge: number,
        maxSizeMB: number
    ): Promise<void> {
        try {
            await this.loadBackupIndex(backupLocation);

            const now = Date.now();
            const maxAgeMs = maxAge * 24 * 60 * 60 * 1000;
            const maxSizeBytes = maxSizeMB * 1024 * 1024;

            // Find old backups
            const oldBackups = Array.from(this.backupIndex.values()).filter(backup => 
                now - backup.timestamp > maxAgeMs
            );

            // Calculate total size
            let totalSize = 0;
            const allBackups = Array.from(this.backupIndex.values()).sort((a, b) => b.timestamp - a.timestamp);
            
            for (const backup of allBackups) {
                totalSize += backup.fileSize;
            }

            // Delete old backups
            for (const backup of oldBackups) {
                await this.deleteBackup(backup.id);
                console.log(`🧹 Cleaned old backup: ${backup.fileName}`);
            }

            // Delete largest backups if size limit exceeded
            if (totalSize > maxSizeBytes) {
                const sortedBySize = allBackups.sort((a, b) => b.fileSize - a.fileSize);
                
                for (const backup of sortedBySize) {
                    if (totalSize <= maxSizeBytes) break;
                    
                    await this.deleteBackup(backup.id);
                    totalSize -= backup.fileSize;
                    console.log(`🧹 Cleaned large backup: ${backup.fileName}`);
                }
            }

        } catch (error) {
            console.warn('Failed to clean old backups:', error);
        }
    }

    /**
     * Confirms deletion with user
     */
    private static async confirmDeletion(filesToDelete: BrokenCCAnalysisResult[]): Promise<boolean> {
        // In a real implementation, this would show a UI dialog
        // For now, return true for auto-deletion
        console.log(`⚠️  About to delete ${filesToDelete.length} broken CC files:`);
        filesToDelete.forEach(file => {
            console.log(`   - ${file.fileName} (${file.overallSeverity})`);
        });
        
        // In a real UI, this would be a confirmation dialog
        return true;
    }

    /**
     * Ensures backup directory exists
     */
    private static async ensureBackupDirectory(backupLocation: string): Promise<void> {
        if (!fs.existsSync(backupLocation)) {
            await fs.promises.mkdir(backupLocation, { recursive: true });
        }
    }

    /**
     * Deletes a file safely
     */
    private static async deleteFile(filePath: string): Promise<void> {
        if (!fs.existsSync(filePath)) {
            throw new Error(`File not found: ${filePath}`);
        }

        await fs.promises.unlink(filePath);
    }

    /**
     * Loads backup index from file
     */
    private static async loadBackupIndex(backupLocation: string): Promise<void> {
        const indexPath = path.join(backupLocation, this.BACKUP_INDEX_FILE);
        
        try {
            if (fs.existsSync(indexPath)) {
                const data = await fs.promises.readFile(indexPath, 'utf8');
                const backups = JSON.parse(data) as DeletionBackup[];
                
                this.backupIndex.clear();
                for (const backup of backups) {
                    this.backupIndex.set(backup.id, backup);
                }
            }
        } catch (error) {
            console.warn('Failed to load backup index:', error);
            this.backupIndex.clear();
        }
    }

    /**
     * Saves backup index to file
     */
    private static async saveBackupIndex(backupLocation: string): Promise<void> {
        const indexPath = path.join(backupLocation, this.BACKUP_INDEX_FILE);
        
        try {
            const backups = Array.from(this.backupIndex.values());
            await fs.promises.writeFile(indexPath, JSON.stringify(backups, null, 2));
        } catch (error) {
            console.error('Failed to save backup index:', error);
        }
    }

    /**
     * Creates an empty operation for when no files need deletion
     */
    private static createEmptyOperation(): DeletionOperation {
        return {
            id: `empty-${Date.now()}`,
            files: [],
            backupLocation: '',
            reason: 'No files to delete',
            timestamp: Date.now(),
            status: 'completed',
            backups: [],
            errors: []
        };
    }

    /**
     * Gets deletion statistics
     */
    public static async getDeletionStats(backupLocation?: string): Promise<{
        totalBackups: number;
        totalBackupSize: number;
        oldestBackup: number | null;
        newestBackup: number | null;
        backupsByType: Map<string, number>;
    }> {
        const location = backupLocation || this.DEFAULT_BACKUP_DIR;
        await this.loadBackupIndex(location);

        const backups = Array.from(this.backupIndex.values());
        const backupsByType = new Map<string, number>();

        let totalSize = 0;
        let oldest: number | null = null;
        let newest: number | null = null;

        for (const backup of backups) {
            totalSize += backup.fileSize;
            
            if (oldest === null || backup.timestamp < oldest) {
                oldest = backup.timestamp;
            }
            if (newest === null || backup.timestamp > newest) {
                newest = backup.timestamp;
            }

            // Count by issue type
            for (const issue of backup.issues) {
                backupsByType.set(issue.type, (backupsByType.get(issue.type) || 0) + 1);
            }
        }

        return {
            totalBackups: backups.length,
            totalBackupSize: totalSize,
            oldestBackup: oldest,
            newestBackup: newest,
            backupsByType
        };
    }
}
