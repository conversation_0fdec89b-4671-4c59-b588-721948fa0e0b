/**
 * Unified Test Suite for Simonitor
 * 
 * Consolidates all testing functionality into a single comprehensive test suite.
 * Replaces multiple scattered test files with organized test categories.
 */

import * as fs from 'fs';
import * as path from 'path';
import { PackageAnalysisService } from '../services/analysis/PackageAnalysisService';
import { UnifiedDependencyAnalyzer } from '../services/analysis/dependencies/UnifiedDependencyAnalyzer';
import { UnifiedConflictDetector } from '../services/analysis/conflicts/UnifiedConflictDetector';
import { ContentAnalysisService } from '../services/analysis/content/ContentAnalysisService';

interface TestSuiteOptions {
    testCategory?: 'all' | 'analysis' | 'conflicts' | 'metadata' | 'performance' | 'ui';
    maxFiles?: number;
    verbose?: boolean;
    assetsPath?: string;
}

interface TestResults {
    category: string;
    totalTests: number;
    passedTests: number;
    failedTests: number;
    duration: number;
    details: TestDetail[];
}

interface TestDetail {
    testName: string;
    status: 'passed' | 'failed' | 'skipped';
    duration: number;
    error?: string;
    metrics?: Record<string, any>;
}

/**
 * Unified Test Suite - comprehensive testing for all Simonitor functionality
 */
export class UnifiedTestSuite {
    
    private static readonly DEFAULT_ASSETS_PATH = path.join(__dirname, '../../assets');
    
    /**
     * Runs the complete test suite
     */
    public static async runTestSuite(options: TestSuiteOptions = {}): Promise<TestResults[]> {
        const {
            testCategory = 'all',
            maxFiles = 10,
            verbose = true,
            assetsPath = this.DEFAULT_ASSETS_PATH
        } = options;
        
        console.log('🧪 Starting Simonitor Unified Test Suite');
        console.log(`   Category: ${testCategory}`);
        console.log(`   Max Files: ${maxFiles}`);
        console.log(`   Assets Path: ${assetsPath}\n`);
        
        const results: TestResults[] = [];
        
        // Run test categories based on selection
        if (testCategory === 'all' || testCategory === 'analysis') {
            results.push(await this.runAnalysisTests(assetsPath, maxFiles, verbose));
        }
        
        if (testCategory === 'all' || testCategory === 'conflicts') {
            results.push(await this.runConflictTests(assetsPath, maxFiles, verbose));
        }
        
        if (testCategory === 'all' || testCategory === 'metadata') {
            results.push(await this.runMetadataTests(assetsPath, maxFiles, verbose));
        }
        
        if (testCategory === 'all' || testCategory === 'performance') {
            results.push(await this.runPerformanceTests(assetsPath, maxFiles, verbose));
        }
        
        // Print summary
        this.printTestSummary(results);
        
        return results;
    }
    
    /**
     * Tests core analysis functionality
     */
    private static async runAnalysisTests(assetsPath: string, maxFiles: number, verbose: boolean): Promise<TestResults> {
        const startTime = performance.now();
        const testDetails: TestDetail[] = [];
        
        console.log('📊 Running Analysis Tests...');
        
        try {
            const modFiles = this.getTestFiles(assetsPath, maxFiles);
            
            for (const filePath of modFiles) {
                const testStart = performance.now();
                const fileName = path.basename(filePath);
                
                try {
                    const buffer = fs.readFileSync(filePath);
                    
                    // Test basic analysis
                    const quickResult = await PackageAnalysisService.analyzeQuick(buffer, fileName);
                    const detailedResult = await PackageAnalysisService.analyzeDetailed(buffer, fileName);
                    
                    // Test content analysis
                    const contentResult = new ContentAnalysisService().analyzeModContent(buffer, fileName);
                    
                    // Test dependency analysis
                    const dependencyResult = await UnifiedDependencyAnalyzer.analyze(buffer, fileName);
                    
                    const testDuration = performance.now() - testStart;
                    
                    testDetails.push({
                        testName: `Analysis: ${fileName}`,
                        status: 'passed',
                        duration: testDuration,
                        metrics: {
                            quickAnalysisTime: quickResult ? 'success' : 'failed',
                            detailedAnalysisTime: detailedResult ? 'success' : 'failed',
                            contentAnalysisTime: contentResult ? 'success' : 'failed',
                            dependencyAnalysisTime: dependencyResult ? 'success' : 'failed',
                            processingSpeed: `${(1000 / testDuration).toFixed(2)} files/sec`
                        }
                    });
                    
                    if (verbose) {
                        console.log(`   ✅ ${fileName} - ${testDuration.toFixed(2)}ms`);
                    }
                    
                } catch (error) {
                    const testDuration = performance.now() - testStart;
                    testDetails.push({
                        testName: `Analysis: ${fileName}`,
                        status: 'failed',
                        duration: testDuration,
                        error: error instanceof Error ? error.message : String(error)
                    });
                    
                    if (verbose) {
                        console.log(`   ❌ ${fileName} - Error: ${error}`);
                    }
                }
            }
            
        } catch (error) {
            testDetails.push({
                testName: 'Analysis Test Setup',
                status: 'failed',
                duration: 0,
                error: error instanceof Error ? error.message : String(error)
            });
        }
        
        const duration = performance.now() - startTime;
        const passedTests = testDetails.filter(t => t.status === 'passed').length;
        const failedTests = testDetails.filter(t => t.status === 'failed').length;
        
        return {
            category: 'Analysis',
            totalTests: testDetails.length,
            passedTests,
            failedTests,
            duration,
            details: testDetails
        };
    }
    
    /**
     * Tests conflict detection functionality
     */
    private static async runConflictTests(assetsPath: string, maxFiles: number, verbose: boolean): Promise<TestResults> {
        const startTime = performance.now();
        const testDetails: TestDetail[] = [];
        
        console.log('⚔️ Running Conflict Detection Tests...');
        
        try {
            const modFiles = this.getTestFiles(assetsPath, maxFiles);
            const modAnalyses = new Map();
            const packages = new Map();
            
            // Analyze all mods first
            for (const filePath of modFiles) {
                const fileName = path.basename(filePath);
                const buffer = fs.readFileSync(filePath);
                
                try {
                    const contentAnalysis = new ContentAnalysisService().analyzeModContent(buffer, fileName);
                    modAnalyses.set(fileName, contentAnalysis);
                    
                    // For package files, also store the package
                    if (fileName.endsWith('.package')) {
                        // This would need proper package parsing
                        packages.set(fileName, null); // Simplified for now
                    }
                } catch (error) {
                    console.log(`   Warning: Could not analyze ${fileName} for conflicts`);
                }
            }
            
            // Test conflict detection
            const testStart = performance.now();
            const conflictResult = UnifiedConflictDetector.detectConflicts(modAnalyses, packages);
            const testDuration = performance.now() - testStart;
            
            testDetails.push({
                testName: 'Conflict Detection',
                status: 'passed',
                duration: testDuration,
                metrics: {
                    totalConflicts: conflictResult.totalConflicts,
                    criticalConflicts: conflictResult.conflictsBySeverity.critical,
                    highConflicts: conflictResult.conflictsBySeverity.high,
                    mediumConflicts: conflictResult.conflictsBySeverity.medium,
                    lowConflicts: conflictResult.conflictsBySeverity.low,
                    overallRisk: conflictResult.overallRisk
                }
            });
            
            if (verbose) {
                console.log(`   ✅ Conflict Detection - ${conflictResult.totalConflicts} conflicts found`);
                console.log(`      Critical: ${conflictResult.conflictsBySeverity.critical}`);
                console.log(`      High: ${conflictResult.conflictsBySeverity.high}`);
                console.log(`      Medium: ${conflictResult.conflictsBySeverity.medium}`);
                console.log(`      Low: ${conflictResult.conflictsBySeverity.low}`);
            }
            
        } catch (error) {
            testDetails.push({
                testName: 'Conflict Detection',
                status: 'failed',
                duration: 0,
                error: error instanceof Error ? error.message : String(error)
            });
        }
        
        const duration = performance.now() - startTime;
        const passedTests = testDetails.filter(t => t.status === 'passed').length;
        const failedTests = testDetails.filter(t => t.status === 'failed').length;
        
        return {
            category: 'Conflicts',
            totalTests: testDetails.length,
            passedTests,
            failedTests,
            duration,
            details: testDetails
        };
    }
    
    /**
     * Tests metadata extraction functionality
     */
    private static async runMetadataTests(assetsPath: string, maxFiles: number, verbose: boolean): Promise<TestResults> {
        const startTime = performance.now();
        const testDetails: TestDetail[] = [];
        
        console.log('📝 Running Metadata Extraction Tests...');
        
        try {
            const modFiles = this.getTestFiles(assetsPath, maxFiles);
            let successfulExtractions = 0;
            let authorDetections = 0;
            let nameExtractions = 0;
            
            for (const filePath of modFiles) {
                const testStart = performance.now();
                const fileName = path.basename(filePath);
                
                try {
                    const buffer = fs.readFileSync(filePath);
                    const detailedResult = await PackageAnalysisService.analyzeDetailed(buffer, fileName);
                    
                    if (detailedResult) {
                        successfulExtractions++;
                        
                        // Check for author detection
                        if (detailedResult.metadata?.author && detailedResult.metadata.author !== 'Unknown') {
                            authorDetections++;
                        }
                        
                        // Check for name extraction
                        if (detailedResult.metadata?.modName && detailedResult.metadata.modName !== fileName) {
                            nameExtractions++;
                        }
                    }
                    
                    const testDuration = performance.now() - testStart;
                    
                    testDetails.push({
                        testName: `Metadata: ${fileName}`,
                        status: 'passed',
                        duration: testDuration,
                        metrics: {
                            hasAuthor: detailedResult?.metadata?.author !== 'Unknown',
                            hasModName: detailedResult?.metadata?.modName !== fileName,
                            hasDescription: !!detailedResult?.metadata?.description
                        }
                    });
                    
                } catch (error) {
                    const testDuration = performance.now() - testStart;
                    testDetails.push({
                        testName: `Metadata: ${fileName}`,
                        status: 'failed',
                        duration: testDuration,
                        error: error instanceof Error ? error.message : String(error)
                    });
                }
            }
            
            // Add summary test
            testDetails.push({
                testName: 'Metadata Extraction Summary',
                status: 'passed',
                duration: 0,
                metrics: {
                    extractionSuccessRate: `${Math.round((successfulExtractions / modFiles.length) * 100)}%`,
                    authorDetectionRate: `${Math.round((authorDetections / modFiles.length) * 100)}%`,
                    nameExtractionRate: `${Math.round((nameExtractions / modFiles.length) * 100)}%`
                }
            });
            
            if (verbose) {
                console.log(`   📊 Metadata Summary:`);
                console.log(`      Extraction Success: ${Math.round((successfulExtractions / modFiles.length) * 100)}%`);
                console.log(`      Author Detection: ${Math.round((authorDetections / modFiles.length) * 100)}%`);
                console.log(`      Name Extraction: ${Math.round((nameExtractions / modFiles.length) * 100)}%`);
            }
            
        } catch (error) {
            testDetails.push({
                testName: 'Metadata Test Setup',
                status: 'failed',
                duration: 0,
                error: error instanceof Error ? error.message : String(error)
            });
        }
        
        const duration = performance.now() - startTime;
        const passedTests = testDetails.filter(t => t.status === 'passed').length;
        const failedTests = testDetails.filter(t => t.status === 'failed').length;
        
        return {
            category: 'Metadata',
            totalTests: testDetails.length,
            passedTests,
            failedTests,
            duration,
            details: testDetails
        };
    }
    
    /**
     * Tests performance metrics
     */
    private static async runPerformanceTests(assetsPath: string, maxFiles: number, verbose: boolean): Promise<TestResults> {
        const startTime = performance.now();
        const testDetails: TestDetail[] = [];
        
        console.log('⚡ Running Performance Tests...');
        
        try {
            const modFiles = this.getTestFiles(assetsPath, maxFiles);
            const processingTimes: number[] = [];
            
            for (const filePath of modFiles) {
                const testStart = performance.now();
                const fileName = path.basename(filePath);
                
                try {
                    const buffer = fs.readFileSync(filePath);
                    await PackageAnalysisService.analyzeQuick(buffer, fileName);
                    
                    const testDuration = performance.now() - testStart;
                    processingTimes.push(testDuration);
                    
                } catch (error) {
                    // Skip failed files for performance testing
                }
            }
            
            // Calculate performance metrics
            const avgProcessingTime = processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length;
            const filesPerSecond = 1000 / avgProcessingTime;
            const targetFilesPerSecond = 6; // From handoff document
            
            testDetails.push({
                testName: 'Processing Speed',
                status: filesPerSecond >= targetFilesPerSecond ? 'passed' : 'failed',
                duration: avgProcessingTime,
                metrics: {
                    averageProcessingTime: `${avgProcessingTime.toFixed(2)}ms`,
                    filesPerSecond: `${filesPerSecond.toFixed(2)} files/sec`,
                    targetMet: filesPerSecond >= targetFilesPerSecond,
                    target: `${targetFilesPerSecond} files/sec`
                }
            });
            
            if (verbose) {
                console.log(`   ⚡ Performance Results:`);
                console.log(`      Average Processing Time: ${avgProcessingTime.toFixed(2)}ms`);
                console.log(`      Files per Second: ${filesPerSecond.toFixed(2)}`);
                console.log(`      Target (6 files/sec): ${filesPerSecond >= targetFilesPerSecond ? '✅ MET' : '❌ NOT MET'}`);
            }
            
        } catch (error) {
            testDetails.push({
                testName: 'Performance Test Setup',
                status: 'failed',
                duration: 0,
                error: error instanceof Error ? error.message : String(error)
            });
        }
        
        const duration = performance.now() - startTime;
        const passedTests = testDetails.filter(t => t.status === 'passed').length;
        const failedTests = testDetails.filter(t => t.status === 'failed').length;
        
        return {
            category: 'Performance',
            totalTests: testDetails.length,
            passedTests,
            failedTests,
            duration,
            details: testDetails
        };
    }
    
    /**
     * Gets test files from assets directory
     */
    private static getTestFiles(assetsPath: string, maxFiles: number): string[] {
        if (!fs.existsSync(assetsPath)) {
            console.log(`⚠️ Assets directory not found: ${assetsPath}`);
            return [];
        }
        
        const files = fs.readdirSync(assetsPath)
            .filter(file => file.endsWith('.package') || file.endsWith('.ts4script'))
            .slice(0, maxFiles)
            .map(file => path.join(assetsPath, file));
        
        console.log(`   Found ${files.length} test files`);
        return files;
    }
    
    /**
     * Prints test summary
     */
    private static printTestSummary(results: TestResults[]): void {
        console.log('\n📋 Test Suite Summary:');
        console.log('========================');
        
        let totalTests = 0;
        let totalPassed = 0;
        let totalFailed = 0;
        let totalDuration = 0;
        
        for (const result of results) {
            totalTests += result.totalTests;
            totalPassed += result.passedTests;
            totalFailed += result.failedTests;
            totalDuration += result.duration;
            
            const successRate = Math.round((result.passedTests / result.totalTests) * 100);
            console.log(`${result.category}: ${result.passedTests}/${result.totalTests} (${successRate}%) - ${result.duration.toFixed(2)}ms`);
        }
        
        const overallSuccessRate = Math.round((totalPassed / totalTests) * 100);
        console.log('------------------------');
        console.log(`Overall: ${totalPassed}/${totalTests} (${overallSuccessRate}%) - ${totalDuration.toFixed(2)}ms`);
        
        if (overallSuccessRate >= 90) {
            console.log('🎉 Test suite PASSED!');
        } else if (overallSuccessRate >= 70) {
            console.log('⚠️ Test suite passed with warnings');
        } else {
            console.log('❌ Test suite FAILED');
        }
    }
}

// CLI execution
if (require.main === module) {
    const args = process.argv.slice(2);
    const category = args[0] as any || 'all';
    const maxFiles = parseInt(args[1]) || 10;
    
    UnifiedTestSuite.runTestSuite({
        testCategory: category,
        maxFiles,
        verbose: true
    }).catch(console.error);
}
