/**
 * Unified Dependency Analyzer
 * 
 * Consolidates all dependency analysis functionality from multiple scattered services:
 * - deep/DependencyAnalyzer.ts
 * - specialized/common/DependencyAnalyzer.ts  
 * - dependencies/ComprehensiveDependencyService.ts
 * 
 * Provides comprehensive dependency tracking, validation, and conflict detection.
 */

import { Package } from '@s4tk/models';
import type { ResourceEntry } from '@s4tk/models/types';
import { PackageParserService } from '../../shared/PackageParserService';
import { ScriptAnalysisUtils } from '../../shared/ScriptAnalysisUtils';
import { RESOURCE_GROUPS } from '../../../constants/ResourceTypeRegistry';

// Unified interfaces
export interface UnifiedDependencyInfo {
    // Basic dependency info
    requiredMods: string[];
    requiredPacks: string[];
    conflicts: string[];
    features: string[];
    dependencies: ModDependency[];
    riskLevel: 'low' | 'medium' | 'high';
    
    // Enhanced analysis
    packDependencies: PackDependency[];
    potentialConflicts: ConflictInfo[];
    coreModDependencies: CoreModDependency[];
    missingDependencies: MissingDependency[];
    recommendations: string[];
}

export interface ModDependency {
    type: 'script' | 'resource' | 'framework';
    name: string;
    version?: string;
    required: boolean;
    source: 'import' | 'resource_reference' | 'manifest';
    confidence: number;
}

export interface PackDependency {
    type: 'expansion' | 'game' | 'stuff';
    name: string;
    code: string; // EP01, GP01, etc.
    required: boolean;
    confidence: number;
}

export interface ConflictInfo {
    type: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
    affectedMods?: string[];
}

export interface CoreModDependency {
    name: string;
    author: string;
    required: boolean;
    detectionMethod: string;
    downloadUrl?: string;
}

export interface MissingDependency {
    name: string;
    type: 'core_mod' | 'framework' | 'expansion_pack';
    severity: 'critical' | 'high' | 'medium' | 'low';
    description: string;
    downloadUrl?: string;
}

/**
 * Unified Dependency Analyzer - consolidates all dependency analysis functionality
 */
export class UnifiedDependencyAnalyzer {
    
    // Core mod database (consolidated from ComprehensiveDependencyService)
    private static readonly CORE_MODS_DATABASE = [
        {
            id: 'sims4communitylib',
            name: 'Sims 4 Community Library',
            author: 'ColonolNutty',
            detectionPatterns: ['s4cl', 'sims4communitylib', 'colonolnutty'],
            isFramework: true
        },
        {
            id: 'lot51_core',
            name: 'Lot 51 Core Library',
            author: 'Lot 51',
            detectionPatterns: ['lot51', 'lot_51', 'core_library'],
            isFramework: true
        },
        {
            id: 'wickedwhims',
            name: 'WickedWhims',
            author: 'TURBODRIVER',
            detectionPatterns: ['wickedwhims', 'turbodriver', 'ww_'],
            isFramework: false
        }
    ];
    
    /**
     * Main analysis method - analyzes both package and script files
     */
    public static async analyze(
        buffer: Buffer,
        fileName: string,
        filePath?: string
    ): Promise<UnifiedDependencyInfo> {
        
        if (fileName.toLowerCase().endsWith('.ts4script')) {
            return this.analyzeScriptDependencies(buffer, fileName);
        } else {
            return this.analyzePackageDependencies(buffer, fileName);
        }
    }
    
    /**
     * Analyzes script mod dependencies
     */
    private static analyzeScriptDependencies(buffer: Buffer, fileName: string): UnifiedDependencyInfo {
        const result: UnifiedDependencyInfo = this.createEmptyResult();
        
        try {
            const content = buffer.toString('binary');
            
            // Analyze Python imports for framework dependencies
            this.analyzeScriptImports(content, result);
            
            // Analyze injection patterns for conflict potential
            this.analyzeInjectionPatterns(content, result);
            
            // Analyze game system references for pack requirements
            this.analyzeGameSystemReferences(content, result);
            
            // Detect core mod dependencies
            this.detectCoreModDependencies(fileName, content, result);
            
            // Calculate overall risk level
            result.riskLevel = this.calculateRiskLevel(result);
            
        } catch (error) {
            console.error(`[UnifiedDependencyAnalyzer] Error analyzing script ${fileName}:`, error);
            result.riskLevel = 'medium';
        }
        
        return result;
    }
    
    /**
     * Analyzes package mod dependencies
     */
    private static analyzePackageDependencies(buffer: Buffer, fileName: string): UnifiedDependencyInfo {
        const result: UnifiedDependencyInfo = this.createEmptyResult();
        
        try {
            const parseResult = PackageParserService.parsePackage(buffer, fileName, {
                enableCaching: true
            });
            
            if (parseResult.package) {
                // Analyze resource types for pack requirements
                this.analyzeResourcePackRequirements(parseResult.package, result);
                
                // Analyze tuning references for dependencies
                this.analyzeTuningDependencies(parseResult.package, result);
                
                // Analyze resource overwrites for conflicts
                this.analyzeResourceConflicts(parseResult.package, result);
                
                // Detect core mod dependencies from filename
                this.detectCoreModDependencies(fileName, '', result);
            }
            
            result.riskLevel = this.calculateRiskLevel(result);
            
        } catch (error) {
            console.error(`[UnifiedDependencyAnalyzer] Error analyzing package ${fileName}:`, error);
            result.riskLevel = 'medium';
        }
        
        return result;
    }
    
    /**
     * Creates empty result structure
     */
    private static createEmptyResult(): UnifiedDependencyInfo {
        return {
            requiredMods: [],
            requiredPacks: [],
            conflicts: [],
            features: [],
            dependencies: [],
            riskLevel: 'low',
            packDependencies: [],
            potentialConflicts: [],
            coreModDependencies: [],
            missingDependencies: [],
            recommendations: []
        };
    }
    
    /**
     * Analyzes Python imports to detect framework dependencies
     */
    private static analyzeScriptImports(content: string, result: UnifiedDependencyInfo): void {
        const importPatterns = [
            /import\s+(\w+)/g,
            /from\s+(\w+)\s+import/g,
            /from\s+(\w+\.\w+)\s+import/g
        ];
        
        for (const pattern of importPatterns) {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                const importName = match[1].toLowerCase();
                
                // Check against core mods database
                for (const coreMod of this.CORE_MODS_DATABASE) {
                    for (const detectionPattern of coreMod.detectionPatterns) {
                        if (importName.includes(detectionPattern.toLowerCase())) {
                            if (!result.requiredMods.includes(coreMod.name)) {
                                result.requiredMods.push(coreMod.name);
                                result.dependencies.push({
                                    type: 'framework',
                                    name: coreMod.name,
                                    required: true,
                                    source: 'import',
                                    confidence: 0.9
                                });
                                result.coreModDependencies.push({
                                    name: coreMod.name,
                                    author: coreMod.author,
                                    required: true,
                                    detectionMethod: 'import_analysis'
                                });
                            }
                            break;
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Analyzes injection patterns for conflict potential
     */
    private static analyzeInjectionPatterns(content: string, result: UnifiedDependencyInfo): void {
        const injectionPatterns = [
            /@inject_to/g,
            /@override/g,
            /inject_to\(/g,
            /override_function/g
        ];
        
        let injectionCount = 0;
        for (const pattern of injectionPatterns) {
            const matches = content.match(pattern);
            if (matches) {
                injectionCount += matches.length;
            }
        }
        
        if (injectionCount > 0) {
            result.potentialConflicts.push({
                type: 'script_injection',
                description: `Uses ${injectionCount} code injection(s) - may conflict with other script mods`,
                severity: injectionCount > 5 ? 'high' : 'medium'
            });
        }
    }
    
    /**
     * Analyzes game system references for pack requirements
     */
    private static analyzeGameSystemReferences(content: string, result: UnifiedDependencyInfo): void {
        const packReferences = {
            'EP01': ['get_together', 'club_', 'gathering'],
            'EP02': ['city_living', 'apartment', 'festival'],
            'EP03': ['cats_dogs', 'pet_', 'veterinarian'],
            'EP04': ['seasons', 'weather', 'holiday'],
            'GP01': ['outdoor_retreat', 'camping', 'herbalism']
        };
        
        for (const [packCode, patterns] of Object.entries(packReferences)) {
            for (const pattern of patterns) {
                if (content.toLowerCase().includes(pattern)) {
                    if (!result.requiredPacks.includes(packCode)) {
                        result.requiredPacks.push(packCode);
                        result.packDependencies.push({
                            type: packCode.startsWith('EP') ? 'expansion' : 'game',
                            name: packCode,
                            code: packCode,
                            required: true,
                            confidence: 0.7
                        });
                    }
                    break;
                }
            }
        }
    }
    
    /**
     * Detects core mod dependencies from filename patterns
     */
    private static detectCoreModDependencies(fileName: string, content: string, result: UnifiedDependencyInfo): void {
        const lowerFileName = fileName.toLowerCase();
        
        for (const coreMod of this.CORE_MODS_DATABASE) {
            for (const pattern of coreMod.detectionPatterns) {
                if (lowerFileName.includes(pattern.toLowerCase())) {
                    if (!result.requiredMods.includes(coreMod.name)) {
                        result.requiredMods.push(coreMod.name);
                        result.coreModDependencies.push({
                            name: coreMod.name,
                            author: coreMod.author,
                            required: true,
                            detectionMethod: 'filename_pattern'
                        });
                    }
                    break;
                }
            }
        }
    }
    
    /**
     * Analyzes resource types for pack requirements
     */
    private static analyzeResourcePackRequirements(s4tkPackage: Package, result: UnifiedDependencyInfo): void {
        // Implementation would analyze resource types to detect pack dependencies
        // This is a simplified version - full implementation would check specific resource patterns
    }
    
    /**
     * Analyzes tuning dependencies
     */
    private static analyzeTuningDependencies(s4tkPackage: Package, result: UnifiedDependencyInfo): void {
        // Implementation would analyze tuning references
    }
    
    /**
     * Analyzes resource conflicts
     */
    private static analyzeResourceConflicts(s4tkPackage: Package, result: UnifiedDependencyInfo): void {
        // Implementation would analyze resource overwrites
    }
    
    /**
     * Calculates overall risk level based on dependencies and conflicts
     */
    private static calculateRiskLevel(result: UnifiedDependencyInfo): 'low' | 'medium' | 'high' {
        let riskScore = 0;
        
        // Add risk for missing dependencies
        riskScore += result.missingDependencies.length * 2;
        
        // Add risk for conflicts
        riskScore += result.potentialConflicts.filter(c => c.severity === 'high').length * 3;
        riskScore += result.potentialConflicts.filter(c => c.severity === 'medium').length * 2;
        riskScore += result.potentialConflicts.filter(c => c.severity === 'low').length * 1;
        
        // Add risk for framework dependencies
        riskScore += result.dependencies.filter(d => d.type === 'framework').length * 1;
        
        if (riskScore >= 6) return 'high';
        if (riskScore >= 3) return 'medium';
        return 'low';
    }
}
