#!/usr/bin/env tsx

/**
 * 🎯 Smart Thumbnail Selection Test
 * 
 * Tests the new smart thumbnail selection algorithm that prioritizes
 * CAS thumbnails (representative images) over RLE2 images (color variations)
 * for primary thumbnail display in the grid.
 */

import fs from 'fs';
import path from 'path';
import { ThumbnailExtractionService } from '../services/visual/ThumbnailExtractionService';

const MODS_PATH = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';

async function testSmartThumbnailSelection() {
    console.log('🎯 Testing Smart Thumbnail Selection Algorithm...\n');

    // Find a few test mods
    const testMods = [
        'KiaraZurk_TiffanyHairstyle.package',  // Hair mod with CAS + RLE2
        'Aurum_female_Jeans_1.package',       // Clothing mod with CAS thumbnails
    ];

    for (const modFile of testMods) {
        const modPath = path.join(MODS_PATH, modFile);
        
        if (!fs.existsSync(modPath)) {
            console.log(`⚠️ Skipping ${modFile} - file not found`);
            continue;
        }

        console.log(`\n📦 Testing: ${modFile}`);
        console.log(`📍 Path: ${modPath}`);
        
        const buffer = fs.readFileSync(modPath);
        console.log(`📏 File size: ${(buffer.length / 1024).toFixed(2)} KB`);

        // Test with smart selection enabled (new behavior)
        console.log('\n🎯 Testing with Smart Selection ENABLED:');
        const smartResult = await ThumbnailExtractionService.extractThumbnails(
            buffer,
            modFile,
            {
                maxThumbnails: 25,
                prioritizeCasThumbnails: true,
                generateFallbacks: true
            }
        );

        if (smartResult.success && smartResult.thumbnails.length > 0) {
            console.log(`✅ Extracted ${smartResult.thumbnails.length} thumbnails`);
            console.log(`🎨 Primary thumbnail: ${smartResult.thumbnails[0].resourceType}`);
            console.log(`📊 Resource type breakdown:`);
            
            const typeCount = new Map<string, number>();
            smartResult.thumbnails.forEach(thumb => {
                const count = typeCount.get(thumb.resourceType) || 0;
                typeCount.set(thumb.resourceType, count + 1);
            });
            
            typeCount.forEach((count, type) => {
                const isPrimary = type === smartResult.thumbnails[0].resourceType;
                console.log(`   ${isPrimary ? '🎯' : '  '} ${type}: ${count} ${isPrimary ? '(PRIMARY)' : ''}`);
            });

            // Show first few thumbnails for analysis
            console.log(`\n🔍 First 3 thumbnails in order:`);
            smartResult.thumbnails.slice(0, 3).forEach((thumb, index) => {
                console.log(`   ${index + 1}. ${thumb.resourceType} (${thumb.extractionMethod})`);
                console.log(`      Size: ${thumb.width}x${thumb.height}, Confidence: ${thumb.confidence}%`);
                console.log(`      Category: ${thumb.category}, Quality: ${thumb.isHighQuality ? 'High' : 'Standard'}`);
            });

        } else {
            console.log(`❌ Failed to extract thumbnails: ${smartResult.errors.join(', ')}`);
        }

        console.log('\n' + '='.repeat(80));
    }

    console.log('\n🎉 Smart Thumbnail Selection Test Complete!');
    console.log('\n📋 Expected Results:');
    console.log('   • CAS Thumbnails should be selected as primary (representative images)');
    console.log('   • RLE2 Images should be organized as variations (color swatches)');
    console.log('   • Primary thumbnail should show the actual mod preview, not texture patterns');
    console.log('   • Grid cards should display meaningful representative images');
}

// Run the test
testSmartThumbnailSelection().catch(console.error);
