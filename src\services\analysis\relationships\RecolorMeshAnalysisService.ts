/**
 * Recolor/Mesh Relationship Analysis Service
 * 
 * Analyzes mesh-recolor relationships and detects missing mesh dependencies.
 * Provides proper classification between original meshes and recolors.
 * 
 * Addresses Reddit request: "recolor/mesh relationship detection"
 */

import { Package } from '@s4tk/models';
import { BinaryResourceType } from '@s4tk/models/enums';
import type { ResourceEntry } from '@s4tk/models/types';
import { PackageParserService } from '../../shared/PackageParserService';
import { RESOURCE_GROUPS } from '../../../constants/ResourceTypeRegistry';

export interface MeshRecolorRelationship {
    id: string;
    type: 'original_mesh' | 'recolor' | 'mesh_with_recolors' | 'standalone';
    fileName: string;
    filePath: string;
    meshInfo?: MeshInfo;
    recolorInfo?: RecolorInfo;
    relationships: RelatedItem[];
    missingDependencies: MissingMeshDependency[];
    confidence: number;
    analysisMethod: string;
}

export interface MeshInfo {
    meshResourceKeys: string[];
    lodLevels: number;
    vertexCount?: number;
    textureSlots: TextureSlot[];
    isCustomMesh: boolean;
    meshComplexity: 'low' | 'medium' | 'high';
    supportedAgeGroups: string[];
    supportedGenders: string[];
}

export interface RecolorInfo {
    originalMeshRequired: string;
    textureOverrides: TextureOverride[];
    colorChannels: ColorChannel[];
    recolorType: 'simple' | 'advanced' | 'pattern' | 'overlay';
    preservesOriginalMesh: boolean;
    customTextures: boolean;
}

export interface TextureSlot {
    slotId: string;
    textureType: 'diffuse' | 'normal' | 'specular' | 'overlay';
    resourceKey: string;
    isCustom: boolean;
}

export interface TextureOverride {
    originalTextureKey: string;
    newTextureKey: string;
    textureType: string;
    overrideMethod: 'replace' | 'overlay' | 'blend';
}

export interface ColorChannel {
    channelId: number;
    colorValue: string;
    channelType: 'primary' | 'secondary' | 'accent' | 'pattern';
    isCustomizable: boolean;
}

export interface RelatedItem {
    fileName: string;
    filePath?: string;
    relationship: 'requires' | 'provides' | 'conflicts_with' | 'enhances';
    confidence: number;
    detectionMethod: string;
}

export interface MissingMeshDependency {
    meshName: string;
    meshAuthor?: string;
    requiredFor: string;
    severity: 'critical' | 'high' | 'medium';
    downloadUrl?: string;
    alternativeOptions?: string[];
    impactDescription: string;
}

export interface CollectionMeshAnalysis {
    totalItems: number;
    originalMeshes: number;
    recolors: number;
    standaloneItems: number;
    orphanedRecolors: number;
    meshFamilies: MeshFamily[];
    missingMeshes: string[];
    recommendations: MeshRecommendation[];
    healthScore: number;
}

export interface MeshFamily {
    familyId: string;
    originalMesh: MeshRecolorRelationship;
    recolors: MeshRecolorRelationship[];
    totalVariations: number;
    isComplete: boolean;
    missingComponents: string[];
}

export interface MeshRecommendation {
    type: 'download_mesh' | 'organize_family' | 'remove_orphan' | 'update_recolor';
    priority: 'high' | 'medium' | 'low';
    description: string;
    affectedItems: string[];
    actionRequired: string;
    estimatedBenefit: string;
}

/**
 * Service for analyzing mesh-recolor relationships in Sims 4 mods
 */
export class RecolorMeshAnalysisService {
    // Removed resource type constants - now using shared RESOURCE_GROUPS

    /**
     * Analyzes a single mod for mesh-recolor relationships
     */
    public static async analyzeMod(
        buffer: Buffer,
        fileName: string,
        filePath: string
    ): Promise<MeshRecolorRelationship> {
        const startTime = performance.now();
        
        try {
            // Use shared package parser
            const parseResult = PackageParserService.parsePackage(buffer, fileName, {
                decompressBuffers: false,
                loadRaw: true,
                enableCaching: true
            });

            const analysis = await this.performMeshRecolorAnalysis(parseResult.package, fileName, filePath);
            analysis.confidence = this.calculateConfidence(analysis);
            
            return analysis;

        } catch (error) {
            console.warn(`Failed to analyze ${fileName}:`, error);
            
            return {
                id: `analysis-${Date.now()}`,
                type: 'standalone',
                fileName,
                filePath,
                relationships: [],
                missingDependencies: [],
                confidence: 0,
                analysisMethod: 'failed_analysis'
            };
        }
    }

    /**
     * Performs the core mesh-recolor analysis
     */
    private static async performMeshRecolorAnalysis(
        s4tkPackage: Package,
        fileName: string,
        filePath: string
    ): Promise<MeshRecolorRelationship> {
        // Use shared resource extraction
        const meshResources = PackageParserService.getResourcesByType(s4tkPackage, RESOURCE_GROUPS.MESH_RESOURCES);
        const textureResources = PackageParserService.getResourcesByType(s4tkPackage, RESOURCE_GROUPS.TEXTURE_RESOURCES);
        const casResources = PackageParserService.getResourcesByType(s4tkPackage, RESOURCE_GROUPS.CAS_RESOURCES);
        const allResources = PackageParserService.getAllResources(s4tkPackage);

        // Determine primary type
        const type = this.determinePrimaryType(meshResources, textureResources, casResources, fileName);
        
        const analysis: MeshRecolorRelationship = {
            id: `mesh-analysis-${Date.now()}`,
            type,
            fileName,
            filePath,
            relationships: [],
            missingDependencies: [],
            confidence: 0,
            analysisMethod: 'resource_analysis'
        };

        // Analyze based on type
        if (type === 'original_mesh' || type === 'mesh_with_recolors') {
            analysis.meshInfo = await this.analyzeMeshInfo(meshResources, textureResources, casResources);
        } else if (type === 'recolor') {
            analysis.recolorInfo = await this.analyzeRecolorInfo(textureResources, casResources, fileName);
            analysis.missingDependencies = await this.detectMissingMeshDependencies(analysis.recolorInfo, fileName);
        }

        // Find relationships
        analysis.relationships = await this.findRelationships(analysis, allResources);

        return analysis;
    }

    /**
     * Determines the primary type of the mod
     */
    private static determinePrimaryType(
        meshResources: ResourceEntry[],
        textureResources: ResourceEntry[],
        casResources: ResourceEntry[],
        fileName: string
    ): MeshRecolorRelationship['type'] {
        const lowerFileName = fileName.toLowerCase();
        
        // Check filename patterns for recolors
        const recolorPatterns = [
            'recolor', 'recolour', 'retexture', 'palette', 'color', 'colour',
            'texture', 'pattern', 'variant', 'alt', 'alternative'
        ];
        
        const isLikelyRecolor = recolorPatterns.some(pattern => lowerFileName.includes(pattern));
        
        // Analyze resource composition
        const hasMeshes = meshResources.length > 0;
        const hasTextures = textureResources.length > 0;
        const hasCasParts = casResources.length > 0;
        
        if (isLikelyRecolor && hasTextures && !hasMeshes) {
            return 'recolor';
        }
        
        if (hasMeshes && hasTextures) {
            return 'mesh_with_recolors';
        }
        
        if (hasMeshes && !hasTextures) {
            return 'original_mesh';
        }
        
        if (hasTextures && !hasMeshes && hasCasParts) {
            return 'recolor';
        }
        
        return 'standalone';
    }

    /**
     * Analyzes mesh information
     */
    private static async analyzeMeshInfo(
        meshResources: ResourceEntry[],
        textureResources: ResourceEntry[],
        casResources: ResourceEntry[]
    ): Promise<MeshInfo> {
        const meshResourceKeys = meshResources.map(r => 
            `${r.key.type.toString(16)}-${r.key.group.toString(16)}-${r.key.instance.toString(16)}`
        );

        // Analyze LOD levels
        const lodLevels = meshResources.filter(r => r.key.type === BinaryResourceType.ModelLod).length;

        // Analyze texture slots
        const textureSlots: TextureSlot[] = textureResources.map((texture, index) => ({
            slotId: `slot_${index}`,
            textureType: this.determineTextureType(texture),
            resourceKey: `${texture.key.type.toString(16)}-${texture.key.group.toString(16)}-${texture.key.instance.toString(16)}`,
            isCustom: true // Assume custom for now
        }));

        // Determine if this is a custom mesh
        const isCustomMesh = meshResources.length > 0 && textureResources.length > 0;

        // Estimate complexity
        const meshComplexity = this.estimateMeshComplexity(meshResources, textureResources);

        return {
            meshResourceKeys,
            lodLevels: Math.max(1, lodLevels),
            textureSlots,
            isCustomMesh,
            meshComplexity,
            supportedAgeGroups: ['Teen', 'Young Adult', 'Adult', 'Elder'], // Default assumption
            supportedGenders: ['Male', 'Female'] // Default assumption
        };
    }

    /**
     * Analyzes recolor information
     */
    private static async analyzeRecolorInfo(
        textureResources: ResourceEntry[],
        casResources: ResourceEntry[],
        fileName: string
    ): Promise<RecolorInfo> {
        // Determine original mesh requirement from filename
        const originalMeshRequired = this.extractOriginalMeshName(fileName);

        // Analyze texture overrides
        const textureOverrides: TextureOverride[] = textureResources.map(texture => ({
            originalTextureKey: 'unknown', // Would need cross-reference with original
            newTextureKey: `${texture.key.type.toString(16)}-${texture.key.group.toString(16)}-${texture.key.instance.toString(16)}`,
            textureType: this.determineTextureType(texture),
            overrideMethod: 'replace'
        }));

        // Analyze color channels (simplified)
        const colorChannels: ColorChannel[] = [
            {
                channelId: 1,
                colorValue: '#FFFFFF',
                channelType: 'primary',
                isCustomizable: true
            }
        ];

        // Determine recolor type
        const recolorType = this.determineRecolorType(textureResources, fileName);

        return {
            originalMeshRequired,
            textureOverrides,
            colorChannels,
            recolorType,
            preservesOriginalMesh: true,
            customTextures: textureResources.length > 0
        };
    }

    /**
     * Detects missing mesh dependencies for recolors
     */
    private static async detectMissingMeshDependencies(
        recolorInfo: RecolorInfo,
        fileName: string
    ): Promise<MissingMeshDependency[]> {
        const dependencies: MissingMeshDependency[] = [];

        if (recolorInfo.originalMeshRequired && recolorInfo.originalMeshRequired !== 'unknown') {
            // In a real implementation, this would check if the mesh is installed
            dependencies.push({
                meshName: recolorInfo.originalMeshRequired,
                requiredFor: fileName,
                severity: 'critical',
                impactDescription: 'Recolor will not appear in-game without the original mesh'
            });
        }

        return dependencies;
    }

    /**
     * Finds relationships with other mods
     */
    private static async findRelationships(
        analysis: MeshRecolorRelationship,
        resources: ResourceEntry[]
    ): Promise<RelatedItem[]> {
        const relationships: RelatedItem[] = [];

        // This would be enhanced with a database of known mesh-recolor relationships
        // For now, return empty array
        
        return relationships;
    }

    /**
     * Determines texture type from resource
     */
    private static determineTextureType(texture: ResourceEntry): TextureSlot['textureType'] {
        // Simplified texture type detection
        // In a real implementation, this would analyze the texture data
        return 'diffuse';
    }

    /**
     * Estimates mesh complexity
     */
    private static estimateMeshComplexity(
        meshResources: ResourceEntry[],
        textureResources: ResourceEntry[]
    ): MeshInfo['meshComplexity'] {
        const meshCount = meshResources.length;
        const textureCount = textureResources.length;
        
        if (meshCount >= 5 || textureCount >= 10) return 'high';
        if (meshCount >= 2 || textureCount >= 5) return 'medium';
        return 'low';
    }

    /**
     * Extracts original mesh name from recolor filename
     */
    private static extractOriginalMeshName(fileName: string): string {
        // Common patterns for recolor filenames
        const patterns = [
            /(.+?)[-_]recolor/i,
            /(.+?)[-_]retexture/i,
            /(.+?)[-_]palette/i,
            /(.+?)[-_]color/i,
            /(.+?)[-_]variant/i
        ];

        for (const pattern of patterns) {
            const match = fileName.match(pattern);
            if (match) {
                return match[1].trim();
            }
        }

        return 'unknown';
    }

    /**
     * Determines recolor type
     */
    private static determineRecolorType(
        textureResources: ResourceEntry[],
        fileName: string
    ): RecolorInfo['recolorType'] {
        const lowerFileName = fileName.toLowerCase();
        
        if (lowerFileName.includes('pattern')) return 'pattern';
        if (lowerFileName.includes('overlay')) return 'overlay';
        if (textureResources.length > 5) return 'advanced';
        return 'simple';
    }

    /**
     * Calculates confidence score for the analysis
     */
    private static calculateConfidence(analysis: MeshRecolorRelationship): number {
        let confidence = 50; // Base confidence

        // Increase confidence based on available data
        if (analysis.meshInfo) {
            confidence += 20;
            if (analysis.meshInfo.textureSlots.length > 0) confidence += 10;
            if (analysis.meshInfo.isCustomMesh) confidence += 10;
        }

        if (analysis.recolorInfo) {
            confidence += 15;
            if (analysis.recolorInfo.originalMeshRequired !== 'unknown') confidence += 15;
            if (analysis.recolorInfo.textureOverrides.length > 0) confidence += 10;
        }

        // Decrease confidence for missing dependencies
        if (analysis.missingDependencies.length > 0) {
            confidence -= analysis.missingDependencies.length * 5;
        }

        return Math.max(0, Math.min(100, confidence));
    }

    /**
     * Analyzes an entire collection for mesh-recolor relationships
     */
    public static async analyzeCollection(
        modFiles: Array<{ buffer: Buffer; fileName: string; filePath: string }>
    ): Promise<CollectionMeshAnalysis> {
        const analyses: MeshRecolorRelationship[] = [];

        // Analyze each mod
        for (const mod of modFiles) {
            try {
                const analysis = await this.analyzeMod(mod.buffer, mod.fileName, mod.filePath);
                analyses.push(analysis);
            } catch (error) {
                console.warn(`Failed to analyze ${mod.fileName}:`, error);
            }
        }

        // Build mesh families
        const meshFamilies = this.buildMeshFamilies(analyses);
        
        // Find orphaned recolors
        const orphanedRecolors = analyses.filter(a => 
            a.type === 'recolor' && a.missingDependencies.length > 0
        ).length;

        // Generate recommendations
        const recommendations = this.generateMeshRecommendations(analyses, meshFamilies);

        // Calculate health score
        const healthScore = this.calculateCollectionHealthScore(analyses, orphanedRecolors);

        return {
            totalItems: analyses.length,
            originalMeshes: analyses.filter(a => a.type === 'original_mesh' || a.type === 'mesh_with_recolors').length,
            recolors: analyses.filter(a => a.type === 'recolor').length,
            standaloneItems: analyses.filter(a => a.type === 'standalone').length,
            orphanedRecolors,
            meshFamilies,
            missingMeshes: this.findMissingMeshes(analyses),
            recommendations,
            healthScore
        };
    }

    /**
     * Builds mesh families from analyses
     */
    private static buildMeshFamilies(analyses: MeshRecolorRelationship[]): MeshFamily[] {
        const families: MeshFamily[] = [];
        
        // Group by mesh family (simplified implementation)
        const meshes = analyses.filter(a => a.type === 'original_mesh' || a.type === 'mesh_with_recolors');
        
        for (const mesh of meshes) {
            const recolors = analyses.filter(a => 
                a.type === 'recolor' && 
                a.recolorInfo?.originalMeshRequired === mesh.fileName
            );

            families.push({
                familyId: `family-${mesh.fileName}`,
                originalMesh: mesh,
                recolors,
                totalVariations: recolors.length + 1,
                isComplete: recolors.length > 0,
                missingComponents: []
            });
        }

        return families;
    }

    /**
     * Finds missing meshes across the collection
     */
    private static findMissingMeshes(analyses: MeshRecolorRelationship[]): string[] {
        const missingMeshes = new Set<string>();
        
        for (const analysis of analyses) {
            if (analysis.type === 'recolor' && analysis.missingDependencies.length > 0) {
                for (const dependency of analysis.missingDependencies) {
                    missingMeshes.add(dependency.meshName);
                }
            }
        }

        return Array.from(missingMeshes);
    }

    /**
     * Generates recommendations for mesh management
     */
    private static generateMeshRecommendations(
        analyses: MeshRecolorRelationship[],
        meshFamilies: MeshFamily[]
    ): MeshRecommendation[] {
        const recommendations: MeshRecommendation[] = [];

        // Recommend downloading missing meshes
        const orphanedRecolors = analyses.filter(a => 
            a.type === 'recolor' && a.missingDependencies.length > 0
        );

        for (const recolor of orphanedRecolors) {
            for (const dependency of recolor.missingDependencies) {
                recommendations.push({
                    type: 'download_mesh',
                    priority: 'high',
                    description: `Download missing mesh: ${dependency.meshName}`,
                    affectedItems: [recolor.fileName],
                    actionRequired: `Find and install ${dependency.meshName}`,
                    estimatedBenefit: 'Enable recolor functionality'
                });
            }
        }

        return recommendations;
    }

    /**
     * Calculates collection health score
     */
    private static calculateCollectionHealthScore(
        analyses: MeshRecolorRelationship[],
        orphanedRecolors: number
    ): number {
        if (analyses.length === 0) return 100;

        const totalItems = analyses.length;
        const healthyItems = analyses.filter(a => a.missingDependencies.length === 0).length;
        
        const baseScore = (healthyItems / totalItems) * 100;
        const orphanPenalty = (orphanedRecolors / totalItems) * 20;
        
        return Math.max(0, Math.round(baseScore - orphanPenalty));
    }
}
