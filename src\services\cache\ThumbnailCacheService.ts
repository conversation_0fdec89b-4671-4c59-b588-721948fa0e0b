/**
 * Modern Thumbnail Caching System for Simonitor
 * 
 * Features:
 * - IndexedDB for persistent storage
 * - LRU cache for memory management
 * - Automatic cache cleanup
 * - Performance optimized for large collections
 */

interface CachedThumbnail {
  id: string;
  modFileName: string;
  imageData: string;
  format: string;
  width: number;
  height: number;
  category: string;
  fileSize: number;
  lastAccessed: number;
  createdAt: number;
  hash: string; // File hash for cache invalidation
}

interface CacheStats {
  totalItems: number;
  totalSize: number;
  hitRate: number;
  lastCleanup: number;
}

class LRUCache<K, V> {
  private capacity: number;
  private cache = new Map<K, V>();
  private accessOrder = new Map<K, number>();
  private accessCounter = 0;

  constructor(capacity: number) {
    this.capacity = capacity;
  }

  get(key: K): V | undefined {
    const value = this.cache.get(key);
    if (value !== undefined) {
      this.accessOrder.set(key, ++this.accessCounter);
    }
    return value;
  }

  set(key: K, value: V): void {
    if (this.cache.size >= this.capacity && !this.cache.has(key)) {
      this.evictLeastRecentlyUsed();
    }
    
    this.cache.set(key, value);
    this.accessOrder.set(key, ++this.accessCounter);
  }

  has(key: K): boolean {
    return this.cache.has(key);
  }

  delete(key: K): boolean {
    this.accessOrder.delete(key);
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
    this.accessOrder.clear();
    this.accessCounter = 0;
  }

  size(): number {
    return this.cache.size;
  }

  private evictLeastRecentlyUsed(): void {
    let lruKey: K | undefined;
    let lruAccess = Infinity;

    for (const [key, access] of this.accessOrder) {
      if (access < lruAccess) {
        lruAccess = access;
        lruKey = key;
      }
    }

    if (lruKey !== undefined) {
      this.delete(lruKey);
    }
  }
}

export class ThumbnailCacheService {
  private static instance: ThumbnailCacheService;
  private db: IDBDatabase | null = null;
  private memoryCache: LRUCache<string, CachedThumbnail>;
  private stats: CacheStats = {
    totalItems: 0,
    totalSize: 0,
    hitRate: 0,
    lastCleanup: Date.now()
  };
  private hits = 0;
  private misses = 0;

  private readonly DB_NAME = 'SimonitorThumbnailCache';
  private readonly DB_VERSION = 1;
  private readonly STORE_NAME = 'thumbnails';
  private readonly MAX_MEMORY_CACHE_SIZE = 500;
  private readonly MAX_CACHE_AGE_MS = 7 * 24 * 60 * 60 * 1000; // 7 days
  private readonly CLEANUP_INTERVAL_MS = 24 * 60 * 60 * 1000; // 24 hours

  private constructor() {
    this.memoryCache = new LRUCache(this.MAX_MEMORY_CACHE_SIZE);
    this.initializeDatabase();
    this.scheduleCleanup();
  }

  public static getInstance(): ThumbnailCacheService {
    if (!ThumbnailCacheService.instance) {
      ThumbnailCacheService.instance = new ThumbnailCacheService();
    }
    return ThumbnailCacheService.instance;
  }

  private async initializeDatabase(): Promise<void> {
    // Skip IndexedDB initialization in Node.js environment
    if (typeof window === 'undefined' || typeof indexedDB === 'undefined') {
      console.warn('IndexedDB not available (Node.js environment), using in-memory cache');
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.DB_NAME, this.DB_VERSION);

      request.onerror = () => {
        console.error('Failed to open IndexedDB:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        this.loadStats();
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        if (!db.objectStoreNames.contains(this.STORE_NAME)) {
          const store = db.createObjectStore(this.STORE_NAME, { keyPath: 'id' });
          store.createIndex('modFileName', 'modFileName', { unique: false });
          store.createIndex('lastAccessed', 'lastAccessed', { unique: false });
          store.createIndex('hash', 'hash', { unique: false });
        }
      };
    });
  }

  public async getThumbnail(modPath: string, fileHash?: string): Promise<CachedThumbnail | null> {
    const cacheKey = this.generateCacheKey(modPath, fileHash);

    // 1. Check memory cache first
    const memoryResult = this.memoryCache.get(cacheKey);
    if (memoryResult) {
      this.hits++;
      memoryResult.lastAccessed = Date.now();
      return memoryResult;
    }

    // 2. Check IndexedDB cache
    const dbResult = await this.getFromIndexedDB(cacheKey);
    if (dbResult) {
      // Validate cache entry
      if (this.isCacheValid(dbResult, fileHash)) {
        this.hits++;
        dbResult.lastAccessed = Date.now();
        
        // Add to memory cache for faster future access
        this.memoryCache.set(cacheKey, dbResult);
        
        // Update access time in IndexedDB
        await this.updateAccessTime(cacheKey, dbResult.lastAccessed);
        
        return dbResult;
      } else {
        // Invalid cache entry, remove it
        await this.deleteThumbnail(cacheKey);
      }
    }

    this.misses++;
    return null;
  }

  public async cacheThumbnail(
    modPath: string,
    thumbnailData: Omit<CachedThumbnail, 'id' | 'lastAccessed' | 'createdAt'>,
    fileHash?: string
  ): Promise<void> {
    const cacheKey = this.generateCacheKey(modPath, fileHash);
    const now = Date.now();

    const cachedThumbnail: CachedThumbnail = {
      ...thumbnailData,
      id: cacheKey,
      lastAccessed: now,
      createdAt: now,
      hash: fileHash || ''
    };

    // Add to memory cache
    this.memoryCache.set(cacheKey, cachedThumbnail);

    // Add to IndexedDB
    await this.saveToIndexedDB(cachedThumbnail);

    // Update stats
    this.stats.totalItems++;
    this.stats.totalSize += this.estimateSize(cachedThumbnail);
  }

  public async deleteThumbnail(cacheKey: string): Promise<void> {
    // Remove from memory cache
    this.memoryCache.delete(cacheKey);

    // Remove from IndexedDB
    if (this.db) {
      const transaction = this.db.transaction([this.STORE_NAME], 'readwrite');
      const store = transaction.objectStore(this.STORE_NAME);
      await new Promise<void>((resolve, reject) => {
        const request = store.delete(cacheKey);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
    }
  }

  public async clearCache(): Promise<void> {
    // Clear memory cache
    this.memoryCache.clear();

    // Clear IndexedDB
    if (this.db) {
      const transaction = this.db.transaction([this.STORE_NAME], 'readwrite');
      const store = transaction.objectStore(this.STORE_NAME);
      await new Promise<void>((resolve, reject) => {
        const request = store.clear();
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
    }

    // Reset stats
    this.stats = {
      totalItems: 0,
      totalSize: 0,
      hitRate: 0,
      lastCleanup: Date.now()
    };
    this.hits = 0;
    this.misses = 0;
  }

  public getStats(): CacheStats & { memoryUsage: number } {
    const hitRate = this.hits + this.misses > 0 ? this.hits / (this.hits + this.misses) : 0;
    
    return {
      ...this.stats,
      hitRate,
      memoryUsage: this.memoryCache.size()
    };
  }

  private generateCacheKey(modPath: string, fileHash?: string): string {
    const baseName = modPath.split(/[/\\]/).pop() || modPath;
    return fileHash ? `${baseName}-${fileHash}` : baseName;
  }

  private async getFromIndexedDB(cacheKey: string): Promise<CachedThumbnail | null> {
    if (!this.db) return null;

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.STORE_NAME], 'readonly');
      const store = transaction.objectStore(this.STORE_NAME);
      const request = store.get(cacheKey);

      request.onsuccess = () => {
        resolve(request.result || null);
      };

      request.onerror = () => {
        console.error('Failed to get from IndexedDB:', request.error);
        resolve(null);
      };
    });
  }

  private async saveToIndexedDB(thumbnail: CachedThumbnail): Promise<void> {
    if (!this.db) return;

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.STORE_NAME], 'readwrite');
      const store = transaction.objectStore(this.STORE_NAME);
      const request = store.put(thumbnail);

      request.onsuccess = () => resolve();
      request.onerror = () => {
        console.error('Failed to save to IndexedDB:', request.error);
        reject(request.error);
      };
    });
  }

  private async updateAccessTime(cacheKey: string, lastAccessed: number): Promise<void> {
    if (!this.db) return;

    const transaction = this.db.transaction([this.STORE_NAME], 'readwrite');
    const store = transaction.objectStore(this.STORE_NAME);
    
    const getRequest = store.get(cacheKey);
    getRequest.onsuccess = () => {
      const thumbnail = getRequest.result;
      if (thumbnail) {
        thumbnail.lastAccessed = lastAccessed;
        store.put(thumbnail);
      }
    };
  }

  private isCacheValid(thumbnail: CachedThumbnail, currentHash?: string): boolean {
    const now = Date.now();
    const age = now - thumbnail.createdAt;

    // Check age
    if (age > this.MAX_CACHE_AGE_MS) {
      return false;
    }

    // Check hash if provided
    if (currentHash && thumbnail.hash && thumbnail.hash !== currentHash) {
      return false;
    }

    return true;
  }

  private estimateSize(thumbnail: CachedThumbnail): number {
    // Rough estimate of memory usage
    return thumbnail.imageData.length + 
           thumbnail.modFileName.length * 2 + 
           thumbnail.category.length * 2 + 
           200; // Overhead for other properties
  }

  private async loadStats(): Promise<void> {
    // Load stats from IndexedDB or initialize
    // This is a simplified version - in production you might store stats separately
    this.stats.lastCleanup = Date.now();
  }

  private scheduleCleanup(): void {
    setInterval(() => {
      this.performCleanup();
    }, this.CLEANUP_INTERVAL_MS);
  }

  private async performCleanup(): Promise<void> {
    if (!this.db) return;

    const cutoffTime = Date.now() - this.MAX_CACHE_AGE_MS;
    const transaction = this.db.transaction([this.STORE_NAME], 'readwrite');
    const store = transaction.objectStore(this.STORE_NAME);
    const index = store.index('lastAccessed');

    const range = IDBKeyRange.upperBound(cutoffTime);
    const request = index.openCursor(range);

    request.onsuccess = (event) => {
      const cursor = (event.target as IDBRequest).result;
      if (cursor) {
        cursor.delete();
        cursor.continue();
      }
    };

    this.stats.lastCleanup = Date.now();
  }
}

export default ThumbnailCacheService;
