#!/usr/bin/env tsx

/**
 * Test script to verify thumbnail extraction is working
 */

import * as fs from 'fs';
import * as path from 'path';
import { ThumbnailExtractionService } from '../services/visual/ThumbnailExtractionService';

// Path to a real mod file for testing
const MODS_PATH = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';

async function testThumbnailExtraction() {
    console.log('🧪 Testing Thumbnail Extraction...\n');
    
    try {
        // Find a CC mod file that should have images
        const ccFiles = findCCFiles(MODS_PATH);
        
        if (ccFiles.length === 0) {
            console.log('❌ No CC files found for testing');
            return;
        }
        
        // Test with the first CC file
        const testFile = ccFiles[0];
        console.log(`📦 Testing with: ${path.basename(testFile)}`);
        console.log(`📍 Path: ${testFile}`);
        
        // Read the file
        const buffer = fs.readFileSync(testFile);
        console.log(`📏 File size: ${(buffer.length / 1024).toFixed(2)} KB`);
        
        // Extract thumbnails
        console.log('\n🔍 Extracting thumbnails...');
        const result = await ThumbnailExtractionService.extractThumbnails(
            buffer,
            path.basename(testFile),
            {
                maxThumbnails: 5,
                preferredFormat: 'webp',
                maxWidth: 256,
                maxHeight: 256,
                prioritizeCasThumbnails: false, // Test RLE2 extraction first
                includeTextures: true,
                generateFallbacks: true
            }
        );
        
        console.log('\n📊 Extraction Results:');
        console.log(`✅ Success: ${result.success}`);
        console.log(`🖼️ Thumbnails extracted: ${result.thumbnailsExtracted}`);
        console.log(`📈 Total images found: ${result.totalImagesFound}`);
        console.log(`⏱️ Processing time: ${result.processingTime.toFixed(2)}ms`);
        
        if (result.errors.length > 0) {
            console.log('\n❌ Errors:');
            result.errors.forEach(error => console.log(`  - ${error}`));
        }
        
        if (result.thumbnails.length > 0) {
            console.log('\n🎨 Extracted Thumbnails:');
            result.thumbnails.forEach((thumb, index) => {
                console.log(`  ${index + 1}. ${thumb.resourceType} (${thumb.extractionMethod})`);
                console.log(`     Size: ${thumb.width}x${thumb.height}, Confidence: ${thumb.confidence}%`);
                console.log(`     Category: ${thumb.category}, Quality: ${thumb.isHighQuality ? 'High' : 'Low'}`);
                console.log(`     Data length: ${thumb.imageData.length} chars`);
                console.log(`     Data preview: ${thumb.imageData.substring(0, 50)}...`);
            });
        } else {
            console.log('\n❌ No thumbnails extracted');
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

function findCCFiles(directory: string): string[] {
    const ccFiles: string[] = [];
    
    try {
        const items = fs.readdirSync(directory);
        
        for (const item of items) {
            const fullPath = path.join(directory, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                // Recursively search subdirectories
                ccFiles.push(...findCCFiles(fullPath));
            } else if (item.toLowerCase().endsWith('.package')) {
                const fileName = item.toLowerCase();
                // Look for CC files (hair, clothing, objects, etc.)
                if (fileName.includes('aurum') || 
                    fileName.includes('felixandre') || 
                    fileName.includes('chateau') ||
                    fileName.includes('hair') ||
                    fileName.includes('cas') ||
                    fileName.includes('clothing')) {
                    ccFiles.push(fullPath);
                }
            }
        }
    } catch (error) {
        console.warn(`⚠️ Could not read directory ${directory}:`, error.message);
    }
    
    return ccFiles;
}

// Run the test
if (require.main === module) {
    testThumbnailExtraction();
}
