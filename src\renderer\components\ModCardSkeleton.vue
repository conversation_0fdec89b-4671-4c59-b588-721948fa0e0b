<template>
  <article 
    class="mod-card-skeleton"
    :class="`mod-card-skeleton--${size}`"
    role="presentation"
    aria-hidden="true"
  >
    <div class="mod-card-skeleton__container">
      <!-- Thumbnail Image Skeleton -->
      <div class="mod-card-skeleton__image">
        <SkeletonLoader 
          variant="rectangle" 
          :animation="animation"
          width="100%" 
          height="100%" 
          rounded
        />
      </div>

      <!-- Content Overlay Skeleton -->
      <div class="mod-card-skeleton__overlay">
        <!-- Title Skeleton -->
        <div class="mod-card-skeleton__title">
          <SkeletonLoader 
            variant="text" 
            :animation="animation"
            :lines="2"
            height="20px"
          />
        </div>
        
        <!-- Author Skeleton -->
        <div class="mod-card-skeleton__author">
          <SkeletonLoader 
            variant="rectangle" 
            :animation="animation"
            width="60%" 
            height="16px" 
          />
        </div>
      </div>

      <!-- Quality Badge Skeleton -->
      <div class="mod-card-skeleton__badge">
        <SkeletonLoader 
          variant="rectangle" 
          :animation="animation"
          width="40px" 
          height="24px" 
          rounded
        />
      </div>

      <!-- File Type Badge Skeleton -->
      <div class="mod-card-skeleton__file-badge">
        <SkeletonLoader 
          variant="rectangle" 
          :animation="animation"
          width="50px" 
          height="24px" 
          rounded
        />
      </div>
    </div>
  </article>
</template>

<script setup lang="ts">
import SkeletonLoader from './SkeletonLoader.vue';

interface Props {
  /** Size variant matching the mod card sizes */
  size?: 'small' | 'medium' | 'large';
  /** Animation type for all skeleton elements */
  animation?: 'pulse' | 'wave' | 'shimmer' | 'none';
  /** Delay before showing skeleton (for fast loading) */
  delay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  animation: 'shimmer',
  delay: 0,
});
</script>

<style scoped>
/* ===== MOD CARD SKELETON BASE ===== */
.mod-card-skeleton {
  background: var(--bg-elevated);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--border-light);
  position: relative;
  /* Match the mod card shadow */
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06),
    var(--shadow-subtle);
}

.mod-card-skeleton__container {
  position: relative;
  width: 100%;
  height: 100%;
}

/* ===== IMAGE SKELETON ===== */
.mod-card-skeleton__image {
  width: 100%;
  aspect-ratio: 16 / 9;
  position: relative;
  background: var(--charcoal-100);
}

/* ===== OVERLAY SKELETON ===== */
.mod-card-skeleton__overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    transparent 100%
  );
  padding: var(--space-4);
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.mod-card-skeleton__title {
  margin-bottom: var(--space-1);
}

.mod-card-skeleton__author {
  opacity: 0.8;
}

/* ===== BADGE SKELETONS ===== */
.mod-card-skeleton__badge {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
}

.mod-card-skeleton__file-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 2;
}

/* ===== SIZE VARIANTS ===== */
.mod-card-skeleton--small {
  min-height: 200px;
}

.mod-card-skeleton--small .mod-card-skeleton__overlay {
  padding: var(--space-3);
}

.mod-card-skeleton--small .mod-card-skeleton__title {
  font-size: var(--text-sm);
}

.mod-card-skeleton--medium {
  min-height: 280px;
}

.mod-card-skeleton--large {
  min-height: 360px;
}

.mod-card-skeleton--large .mod-card-skeleton__overlay {
  padding: var(--space-6);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 640px) {
  .mod-card-skeleton {
    min-height: 240px;
  }
  
  .mod-card-skeleton__overlay {
    padding: var(--space-3);
  }
  
  .mod-card-skeleton__badge,
  .mod-card-skeleton__file-badge {
    top: 6px;
  }
  
  .mod-card-skeleton__badge {
    left: 6px;
  }
  
  .mod-card-skeleton__file-badge {
    right: 6px;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .mod-card-skeleton {
    /* Ensure skeleton is still visible without animation */
    opacity: 0.7;
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .mod-card-skeleton {
    background: var(--charcoal-800);
    border-color: var(--charcoal-700);
  }
  
  .mod-card-skeleton__image {
    background: var(--charcoal-700);
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  .mod-card-skeleton {
    border-width: 2px;
    border-color: var(--charcoal-600);
  }
}
</style>
