import type { ResourceEntry } from '@s4tk/models/types';
import { ModCategory, FileType } from '../../../types/analysis';
import { BinaryResourceType } from '@s4tk/models/enums';
import { RESOURCE_GROUPS } from '../../../constants/ResourceTypeRegistry';
import type { CategoryAnalysisResult } from './types';

/**
 * Specialized analyzer for determining mod categories
 * Uses resource type analysis and pattern matching
 */
export class CategoryAnalyzer {
    
    /**
     * Analyzes resources to determine the most likely category
     */
    public static analyze(resources: ResourceEntry[], filePath: string, fileType: FileType): CategoryAnalysisResult {
        const detectedCategories = this.detectAllCategories(resources, filePath);
        const primaryCategory = this.determinePrimaryCategory(detectedCategories);
        const subcategory = this.determineSubcategory(primaryCategory, resources, filePath);
        const suggestedPath = this.generateSuggestedPath(primaryCategory, subcategory, filePath);
        
        return {
            detectedCategories,
            primaryCategory,
            subcategory,
            suggestedPath
        };
    }
    
    /**
     * Detects all possible categories with confidence scores
     */
    private static detectAllCategories(resources: ResourceEntry[], filePath: string): Array<{
        category: ModCategory;
        confidence: number;
        evidence: string[];
    }> {
        const categories: Array<{
            category: ModCategory;
            confidence: number;
            evidence: string[];
        }> = [];
        
        // Analyze CAS (Create-A-Sim) content
        const casAnalysis = this.analyzeCASContent(resources, filePath);
        if (casAnalysis.confidence > 0) {
            categories.push(casAnalysis);
        }
        
        // Analyze Build/Buy content
        const buildBuyAnalysis = this.analyzeBuildBuyContent(resources, filePath);
        if (buildBuyAnalysis.confidence > 0) {
            categories.push(buildBuyAnalysis);
        }
        
        // Analyze Tuning content
        const tuningAnalysis = this.analyzeTuningContent(resources, filePath);
        if (tuningAnalysis.confidence > 0) {
            categories.push(tuningAnalysis);
        }
        
        // Analyze Override content
        const overrideAnalysis = this.analyzeOverrideContent(resources, filePath);
        if (overrideAnalysis.confidence > 0) {
            categories.push(overrideAnalysis);
        }
        
        // Analyze Script content
        const scriptAnalysis = this.analyzeScriptContent(resources, filePath);
        if (scriptAnalysis.confidence > 0) {
            categories.push(scriptAnalysis);
        }
        
        return categories.sort((a, b) => b.confidence - a.confidence);
    }
    
    /**
     * Analyzes CAS (Create-A-Sim) content
     */
    private static analyzeCASContent(resources: ResourceEntry[], filePath: string): {
        category: ModCategory;
        confidence: number;
        evidence: string[];
    } {
        const evidence: string[] = [];
        let confidence = 0;
        
        // Check for CAS-specific resource types
        const casResourceTypes = resources.filter(r => RESOURCE_GROUPS.CAS_RESOURCES.includes(r.key.type));
        if (casResourceTypes.length > 0) {
            confidence += 0.4;
            evidence.push(`Contains ${casResourceTypes.length} CAS resource(s)`);
        }
        
        // Check filename patterns
        const fileName = filePath.toLowerCase();
        const casPatterns = ['cas', 'hair', 'clothing', 'accessory', 'makeup', 'skin', 'eyes', 'eyebrows'];
        const matchedPatterns = casPatterns.filter(pattern => fileName.includes(pattern));
        if (matchedPatterns.length > 0) {
            confidence += 0.3;
            evidence.push(`Filename contains CAS patterns: ${matchedPatterns.join(', ')}`);
        }
        
        // Check for texture resources (common in CAS)
        const textureResources = resources.filter(r => RESOURCE_GROUPS.TEXTURE_RESOURCES.includes(r.key.type));
        if (textureResources.length > 5) {
            confidence += 0.2;
            evidence.push(`Contains ${textureResources.length} texture resources`);
        }
        
        // Check for SimData resources (CAS items have SimData)
        const simDataResources = resources.filter(r => r.key.type === BinaryResourceType.SimData);
        if (simDataResources.length > 0) {
            confidence += 0.1;
            evidence.push(`Contains ${simDataResources.length} SimData resource(s)`);
        }
        
        return {
            category: ModCategory.CAS_CC,
            confidence: Math.min(confidence, 1.0),
            evidence
        };
    }
    
    /**
     * Analyzes Build/Buy content
     */
    private static analyzeBuildBuyContent(resources: ResourceEntry[], filePath: string): {
        category: ModCategory;
        confidence: number;
        evidence: string[];
    } {
        const evidence: string[] = [];
        let confidence = 0;
        
        // Check for Build/Buy specific resource types
        const buildBuyResourceTypes = resources.filter(r => RESOURCE_GROUPS.OBJECT_RESOURCES.includes(r.key.type));
        if (buildBuyResourceTypes.length > 0) {
            confidence += 0.4;
            evidence.push(`Contains ${buildBuyResourceTypes.length} Build/Buy resource(s)`);
        }
        
        // Check filename patterns
        const fileName = filePath.toLowerCase();
        const buildBuyPatterns = ['furniture', 'decor', 'build', 'buy', 'object', 'clutter', 'lighting'];
        const matchedPatterns = buildBuyPatterns.filter(pattern => fileName.includes(pattern));
        if (matchedPatterns.length > 0) {
            confidence += 0.3;
            evidence.push(`Filename contains Build/Buy patterns: ${matchedPatterns.join(', ')}`);
        }
        
        // Check for large texture resources (common in Build/Buy)
        const largeTextures = resources.filter(r =>
            RESOURCE_GROUPS.TEXTURE_RESOURCES.includes(r.key.type) &&
            r.value?.getBuffer() && r.value.getBuffer().length > 100000
        );
        if (largeTextures.length > 0) {
            confidence += 0.2;
            evidence.push(`Contains ${largeTextures.length} large texture resource(s)`);
        }
        
        return {
            category: ModCategory.BUILD_BUY_CC,
            confidence: Math.min(confidence, 1.0),
            evidence
        };
    }
    
    /**
     * Analyzes Tuning content
     */
    private static analyzeTuningContent(resources: ResourceEntry[], filePath: string): {
        category: ModCategory;
        confidence: number;
        evidence: string[];
    } {
        const evidence: string[] = [];
        let confidence = 0;
        
        // Check for tuning resource types
        const tuningResources = resources.filter(r => RESOURCE_GROUPS.TUNING_RESOURCES.includes(r.key.type));
        if (tuningResources.length > 0) {
            confidence += 0.5;
            evidence.push(`Contains ${tuningResources.length} tuning resource(s)`);
        }
        
        // Check filename patterns
        const fileName = filePath.toLowerCase();
        const tuningPatterns = ['tuning', 'mod', 'gameplay', 'trait', 'career', 'skill'];
        const matchedPatterns = tuningPatterns.filter(pattern => fileName.includes(pattern));
        if (matchedPatterns.length > 0) {
            confidence += 0.3;
            evidence.push(`Filename contains tuning patterns: ${matchedPatterns.join(', ')}`);
        }
        
        // Small file size is typical for tuning mods
        if (resources.length < 20) {
            confidence += 0.2;
            evidence.push('Small resource count typical of tuning mods');
        }
        
        return {
            category: ModCategory.TUNING_MOD,
            confidence: Math.min(confidence, 1.0),
            evidence
        };
    }
    
    /**
     * Analyzes Override content
     */
    private static analyzeOverrideContent(resources: ResourceEntry[], filePath: string): {
        category: ModCategory;
        confidence: number;
        evidence: string[];
    } {
        const evidence: string[] = [];
        let confidence = 0;
        
        // Check filename patterns
        const fileName = filePath.toLowerCase();
        const overridePatterns = ['override', 'replacement', 'replace', 'vanilla', 'basegame', 'fix'];
        const matchedPatterns = overridePatterns.filter(pattern => fileName.includes(pattern));
        if (matchedPatterns.length > 0) {
            confidence += 0.4;
            evidence.push(`Filename contains override patterns: ${matchedPatterns.join(', ')}`);
        }
        
        // Check for override-prone resource types with base game groups
        const overrideResources = resources.filter(r =>
            RESOURCE_GROUPS.CRITICAL_RESOURCES.includes(r.key.type) &&
            (r.key.group === 0x80000000 || r.key.group === 0x00000000)
        );
        if (overrideResources.length > 0) {
            confidence += 0.5;
            evidence.push(`Contains ${overrideResources.length} potential override resource(s)`);
        }
        
        return {
            category: ModCategory.OVERRIDE,
            confidence: Math.min(confidence, 1.0),
            evidence
        };
    }
    
    /**
     * Analyzes Script content
     */
    private static analyzeScriptContent(resources: ResourceEntry[], filePath: string): {
        category: ModCategory;
        confidence: number;
        evidence: string[];
    } {
        const evidence: string[] = [];
        let confidence = 0;
        
        // Check file extension
        if (filePath.toLowerCase().endsWith('.ts4script')) {
            confidence += 0.8;
            evidence.push('File has .ts4script extension');
        }
        
        // Check for script-specific patterns in filename
        const fileName = filePath.toLowerCase();
        const scriptPatterns = ['script', 'mccc', 'ui_cheat', 'mod'];
        const matchedPatterns = scriptPatterns.filter(pattern => fileName.includes(pattern));
        if (matchedPatterns.length > 0) {
            confidence += 0.2;
            evidence.push(`Filename contains script patterns: ${matchedPatterns.join(', ')}`);
        }
        
        return {
            category: ModCategory.SCRIPT_MOD,
            confidence: Math.min(confidence, 1.0),
            evidence
        };
    }
    
    /**
     * Determines the primary category from detected categories
     */
    private static determinePrimaryCategory(detectedCategories: Array<{
        category: ModCategory;
        confidence: number;
        evidence: string[];
    }>): ModCategory {
        if (detectedCategories.length === 0) {
            return ModCategory.UNKNOWN;
        }
        
        return detectedCategories[0].category;
    }
    
    /**
     * Determines subcategory based on primary category and resources
     */
    private static determineSubcategory(primaryCategory: ModCategory, resources: ResourceEntry[], filePath: string): string {
        const fileName = filePath.toLowerCase();
        
        switch (primaryCategory) {
            case ModCategory.CAS_CC:
                if (fileName.includes('hair')) return 'hair';
                if (fileName.includes('clothing') || fileName.includes('dress') || fileName.includes('outfit')) return 'clothing';
                if (fileName.includes('accessory') || fileName.includes('lashes') || fileName.includes('rings') || fileName.includes('necklace')) return 'accessory';
                if (fileName.includes('makeup') || fileName.includes('lipstick') || fileName.includes('eyeliner') || fileName.includes('eyeshadow')) return 'makeup';
                if (fileName.includes('skin')) return 'skin';
                return 'general_cas';
                
            case ModCategory.BUILD_BUY_CC:
                if (fileName.includes('furniture')) return 'furniture';
                if (fileName.includes('decor')) return 'decor';
                if (fileName.includes('lighting')) return 'lighting';
                if (fileName.includes('clutter')) return 'clutter';
                return 'general_buildbuy';
                
            case ModCategory.TUNING_MOD:
                if (fileName.includes('trait')) return 'trait';
                if (fileName.includes('career')) return 'career';
                if (fileName.includes('skill')) return 'skill';
                return 'general_tuning';
                
            case ModCategory.SCRIPT_MOD:
                if (fileName.includes('ui_cheat') || fileName.includes('uicheat')) return 'ui_cheat';
                if (fileName.includes('mccc') || fileName.includes('mc_command_center')) return 'mccc';
                if (fileName.includes('gameplay')) return 'gameplay';
                return 'general_script';
                
            default:
                return 'general';
        }
    }
    
    /**
     * Generates suggested file path based on category and subcategory
     */
    private static generateSuggestedPath(primaryCategory: ModCategory, subcategory: string, originalPath: string): string | undefined {
        const fileName = originalPath.split(/[/\\]/).pop() || 'unknown';
        
        switch (primaryCategory) {
            case ModCategory.CAS_CC:
                return `Mods/CAS/${subcategory}/${fileName}`;
            case ModCategory.BUILD_BUY_CC:
                return `Mods/BuildBuy/${subcategory}/${fileName}`;
            case ModCategory.TUNING_MOD:
                return `Mods/Tuning/${subcategory}/${fileName}`;
            case ModCategory.SCRIPT_MOD:
                return `Mods/Scripts/${fileName}`;
            case ModCategory.OVERRIDE:
                return `Mods/Overrides/${fileName}`;
            default:
                return undefined;
        }
    }
}