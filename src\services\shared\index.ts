/**
 * Shared Services Index
 * 
 * Exports all shared utility services that eliminate code duplication
 * across the Simonitor analysis pipeline.
 */

// Package parsing and resource extraction
export { PackageParserService } from './PackageParserService';
export type { 
    PackageParseOptions, 
    PackageParseResult, 
    ResourceExtractionOptions 
} from './PackageParserService';

// File validation services
export { FileValidationService } from './FileValidationService';
export type {
    BasicValidationResult,
    PackageValidationResult,
    ScriptValidationResult,
    ValidationError,
    ValidationWarning
} from './FileValidationService';

// Script analysis utilities
export { ScriptAnalysisUtils } from './ScriptAnalysisUtils';
export type {
    ImportInfo,
    FrameworkDependency,
    SyntaxValidationResult,
    SyntaxError,
    SyntaxWarning
} from './ScriptAnalysisUtils';
