/**
 * Unified Conflict Detector
 * 
 * Consolidates conflict detection functionality from multiple services:
 * - conflicts/ResourceConflictDetector.ts
 * - organization/ConflictDetector.ts
 * - conflict/ConflictAnalysisService.ts
 * 
 * Provides comprehensive conflict detection for mod collections.
 */

import { Package } from '@s4tk/models';
import type { ResourceEntry } from '@s4tk/models/types';
import type { ModContentAnalysis } from '../content/ContentAnalysisService';

// Unified conflict types
export enum UnifiedConflictType {
    // Resource-level conflicts
    EXACT_DUPLICATE = 'exact_duplicate',
    OVERRIDE_CONFLICT = 'override_conflict',
    HASH_COLLISION = 'hash_collision',
    
    // Content-aware conflicts
    CAS_SEMANTIC_CONFLICT = 'cas_semantic_conflict',
    OBJECT_FUNCTIONAL_CONFLICT = 'object_functional_conflict',
    SCRIPT_MODULE_CONFLICT = 'script_module_conflict',
    GAMEPLAY_SYSTEM_CONFLICT = 'gameplay_system_conflict',
    
    // Organization conflicts
    DEPENDENCY_SEPARATION = 'dependency_separation',
    FOLDER_STRUCTURE = 'folder_structure',
    SCRIPT_MOD_PLACEMENT = 'script_mod_placement'
}

export enum ConflictSeverity {
    LOW = 'low',
    MEDIUM = 'medium',
    HIGH = 'high',
    CRITICAL = 'critical'
}

export interface UnifiedConflict {
    id: string;
    type: UnifiedConflictType;
    severity: ConflictSeverity;
    title: string;
    description: string;
    affectedMods: string[];
    affectedResources?: ResourceInfo[];
    resolution: ConflictResolution;
    autoFixAvailable: boolean;
    userResolution?: 'resolved' | 'ignored' | 'pending';
    detectionMethod: string;
    confidence: number;
}

export interface ResourceInfo {
    tgi: string;
    type: number;
    group: number;
    instance: string;
    modFile: string;
}

export interface ConflictResolution {
    strategy: 'remove_duplicate' | 'choose_one' | 'rename' | 'reorganize' | 'manual';
    description: string;
    steps: string[];
    automaticAction?: () => Promise<void>;
}

export interface ConflictAnalysisResult {
    totalConflicts: number;
    conflictsBySeverity: Record<ConflictSeverity, number>;
    conflictsByType: Record<UnifiedConflictType, number>;
    conflicts: UnifiedConflict[];
    recommendations: string[];
    overallRisk: ConflictSeverity;
}

/**
 * Unified Conflict Detector - consolidates all conflict detection functionality
 */
export class UnifiedConflictDetector {
    
    private static conflictIdCounter = 0;
    
    /**
     * Main conflict detection method
     */
    public static detectConflicts(
        modAnalyses: Map<string, ModContentAnalysis>,
        packages: Map<string, Package | null>
    ): ConflictAnalysisResult {
        
        console.log(`[UnifiedConflictDetector] Analyzing ${modAnalyses.size} mods for conflicts...`);
        
        const conflicts: UnifiedConflict[] = [];
        
        // Build resource map for analysis
        const resourceMap = this.buildResourceMap(packages);
        
        // Detect different types of conflicts
        conflicts.push(...this.detectResourceConflicts(resourceMap));
        conflicts.push(...this.detectContentAwareConflicts(modAnalyses));
        conflicts.push(...this.detectScriptConflicts(modAnalyses));
        conflicts.push(...this.detectOrganizationConflicts(modAnalyses));
        
        // Generate analysis result
        const result = this.generateAnalysisResult(conflicts);
        
        console.log(`[UnifiedConflictDetector] Found ${conflicts.length} conflicts`);
        console.log(`  - Critical: ${result.conflictsBySeverity[ConflictSeverity.CRITICAL]}`);
        console.log(`  - High: ${result.conflictsBySeverity[ConflictSeverity.HIGH]}`);
        console.log(`  - Medium: ${result.conflictsBySeverity[ConflictSeverity.MEDIUM]}`);
        console.log(`  - Low: ${result.conflictsBySeverity[ConflictSeverity.LOW]}`);
        
        return result;
    }
    
    /**
     * Builds resource map for conflict detection
     */
    private static buildResourceMap(packages: Map<string, Package | null>): Map<string, ResourceInfo[]> {
        const resourceMap = new Map<string, ResourceInfo[]>();
        
        for (const [fileName, pkg] of packages) {
            if (!pkg) continue;
            
            for (const [key, entry] of pkg.entries) {
                const tgi = `${entry.type}-${entry.group}-${entry.instance.toString(16)}`;
                
                if (!resourceMap.has(tgi)) {
                    resourceMap.set(tgi, []);
                }
                
                resourceMap.get(tgi)!.push({
                    tgi,
                    type: entry.type,
                    group: entry.group,
                    instance: entry.instance.toString(16),
                    modFile: fileName
                });
            }
        }
        
        return resourceMap;
    }
    
    /**
     * Detects resource-level conflicts (exact duplicates, overrides, hash collisions)
     */
    private static detectResourceConflicts(resourceMap: Map<string, ResourceInfo[]>): UnifiedConflict[] {
        const conflicts: UnifiedConflict[] = [];
        
        for (const [tgi, resources] of resourceMap) {
            if (resources.length > 1) {
                // Check if it's an exact duplicate or override conflict
                const affectedMods = [...new Set(resources.map(r => r.modFile))];
                
                if (affectedMods.length > 1) {
                    conflicts.push({
                        id: this.generateConflictId(),
                        type: UnifiedConflictType.OVERRIDE_CONFLICT,
                        severity: ConflictSeverity.MEDIUM,
                        title: `Resource Override Conflict`,
                        description: `Resource ${tgi} is overridden by multiple mods`,
                        affectedMods,
                        affectedResources: resources,
                        resolution: {
                            strategy: 'choose_one',
                            description: 'Choose which mod should provide this resource',
                            steps: [
                                'Review the conflicting mods',
                                'Decide which version to keep',
                                'Remove or disable the other mods'
                            ]
                        },
                        autoFixAvailable: false,
                        detectionMethod: 'resource_analysis',
                        confidence: 0.9
                    });
                }
            }
        }
        
        return conflicts;
    }
    
    /**
     * Detects content-aware conflicts (CAS items, objects with same function)
     */
    private static detectContentAwareConflicts(modAnalyses: Map<string, ModContentAnalysis>): UnifiedConflict[] {
        const conflicts: UnifiedConflict[] = [];
        
        // Group mods by content type for semantic analysis
        const casMods = new Map<string, ModContentAnalysis[]>();
        const objectMods = new Map<string, ModContentAnalysis[]>();
        
        for (const [fileName, analysis] of modAnalyses) {
            // Group CAS items by category
            if (analysis.casContent && analysis.casContent.length > 0) {
                for (const casItem of analysis.casContent) {
                    const key = `${casItem.category}-${casItem.subcategory}`;
                    if (!casMods.has(key)) {
                        casMods.set(key, []);
                    }
                    casMods.get(key)!.push(analysis);
                }
            }
            
            // Group objects by function
            if (analysis.objectContent && analysis.objectContent.length > 0) {
                for (const objectItem of analysis.objectContent) {
                    const key = objectItem.function || 'unknown';
                    if (!objectMods.has(key)) {
                        objectMods.set(key, []);
                    }
                    objectMods.get(key)!.push(analysis);
                }
            }
        }
        
        // Detect CAS semantic conflicts (only flag if truly problematic)
        for (const [category, mods] of casMods) {
            if (mods.length > 10) { // Only flag if there are many similar items
                const affectedMods = mods.map(m => m.fileName);
                conflicts.push({
                    id: this.generateConflictId(),
                    type: UnifiedConflictType.CAS_SEMANTIC_CONFLICT,
                    severity: ConflictSeverity.LOW,
                    title: `Many Similar CAS Items`,
                    description: `${mods.length} mods provide similar ${category} items - consider organizing`,
                    affectedMods,
                    resolution: {
                        strategy: 'reorganize',
                        description: 'Organize similar items into subfolders',
                        steps: [
                            'Create category-specific subfolders',
                            'Move similar items together',
                            'Review for actual duplicates'
                        ]
                    },
                    autoFixAvailable: false,
                    detectionMethod: 'content_analysis',
                    confidence: 0.6
                });
            }
        }
        
        return conflicts;
    }
    
    /**
     * Detects script mod conflicts
     */
    private static detectScriptConflicts(modAnalyses: Map<string, ModContentAnalysis>): UnifiedConflict[] {
        const conflicts: UnifiedConflict[] = [];
        
        const scriptMods = Array.from(modAnalyses.values()).filter(
            analysis => analysis.fileType === 'script'
        );
        
        // Check for script mods that might conflict
        const gameplayAreas = new Map<string, ModContentAnalysis[]>();
        
        for (const scriptMod of scriptMods) {
            if (scriptMod.scriptContent?.gameplayAreas) {
                for (const area of scriptMod.scriptContent.gameplayAreas) {
                    if (!gameplayAreas.has(area)) {
                        gameplayAreas.set(area, []);
                    }
                    gameplayAreas.get(area)!.push(scriptMod);
                }
            }
        }
        
        // Flag potential conflicts in same gameplay areas
        for (const [area, mods] of gameplayAreas) {
            if (mods.length > 1 && area !== 'general') {
                const affectedMods = mods.map(m => m.fileName);
                conflicts.push({
                    id: this.generateConflictId(),
                    type: UnifiedConflictType.SCRIPT_MODULE_CONFLICT,
                    severity: ConflictSeverity.MEDIUM,
                    title: `Script Mods Affecting Same Area`,
                    description: `${mods.length} script mods affect ${area} - potential conflicts`,
                    affectedMods,
                    resolution: {
                        strategy: 'manual',
                        description: 'Review script mods for compatibility',
                        steps: [
                            'Check mod descriptions for compatibility notes',
                            'Test mods together in-game',
                            'Remove incompatible mods if issues occur'
                        ]
                    },
                    autoFixAvailable: false,
                    detectionMethod: 'script_analysis',
                    confidence: 0.7
                });
            }
        }
        
        return conflicts;
    }
    
    /**
     * Detects organization conflicts
     */
    private static detectOrganizationConflicts(modAnalyses: Map<string, ModContentAnalysis>): UnifiedConflict[] {
        const conflicts: UnifiedConflict[] = [];
        
        // Check for script mods mixed with package mods (organization issue)
        const scriptMods = Array.from(modAnalyses.values()).filter(a => a.fileType === 'script');
        const packageMods = Array.from(modAnalyses.values()).filter(a => a.fileType === 'package');
        
        if (scriptMods.length > 0 && packageMods.length > 0) {
            conflicts.push({
                id: this.generateConflictId(),
                type: UnifiedConflictType.SCRIPT_MOD_PLACEMENT,
                severity: ConflictSeverity.LOW,
                title: `Mixed Script and Package Mods`,
                description: `Consider organizing script mods separately from package mods`,
                affectedMods: [...scriptMods.map(m => m.fileName), ...packageMods.map(m => m.fileName)],
                resolution: {
                    strategy: 'reorganize',
                    description: 'Organize mods by type',
                    steps: [
                        'Create separate folders for script mods',
                        'Move .ts4script files to script folder',
                        'Keep .package files in main mod folders'
                    ]
                },
                autoFixAvailable: false,
                detectionMethod: 'organization_analysis',
                confidence: 0.8
            });
        }
        
        return conflicts;
    }
    
    /**
     * Generates analysis result summary
     */
    private static generateAnalysisResult(conflicts: UnifiedConflict[]): ConflictAnalysisResult {
        const conflictsBySeverity = {
            [ConflictSeverity.CRITICAL]: 0,
            [ConflictSeverity.HIGH]: 0,
            [ConflictSeverity.MEDIUM]: 0,
            [ConflictSeverity.LOW]: 0
        };
        
        const conflictsByType: Record<UnifiedConflictType, number> = {} as any;
        
        for (const conflict of conflicts) {
            conflictsBySeverity[conflict.severity]++;
            conflictsByType[conflict.type] = (conflictsByType[conflict.type] || 0) + 1;
        }
        
        // Determine overall risk
        let overallRisk = ConflictSeverity.LOW;
        if (conflictsBySeverity[ConflictSeverity.CRITICAL] > 0) {
            overallRisk = ConflictSeverity.CRITICAL;
        } else if (conflictsBySeverity[ConflictSeverity.HIGH] > 0) {
            overallRisk = ConflictSeverity.HIGH;
        } else if (conflictsBySeverity[ConflictSeverity.MEDIUM] > 0) {
            overallRisk = ConflictSeverity.MEDIUM;
        }
        
        const recommendations = this.generateRecommendations(conflicts);
        
        return {
            totalConflicts: conflicts.length,
            conflictsBySeverity,
            conflictsByType,
            conflicts,
            recommendations,
            overallRisk
        };
    }
    
    /**
     * Generates recommendations based on detected conflicts
     */
    private static generateRecommendations(conflicts: UnifiedConflict[]): string[] {
        const recommendations: string[] = [];
        
        const criticalConflicts = conflicts.filter(c => c.severity === ConflictSeverity.CRITICAL);
        const highConflicts = conflicts.filter(c => c.severity === ConflictSeverity.HIGH);
        
        if (criticalConflicts.length > 0) {
            recommendations.push(`Address ${criticalConflicts.length} critical conflicts immediately`);
        }
        
        if (highConflicts.length > 0) {
            recommendations.push(`Review ${highConflicts.length} high-priority conflicts`);
        }
        
        const resourceConflicts = conflicts.filter(c => c.type === UnifiedConflictType.OVERRIDE_CONFLICT);
        if (resourceConflicts.length > 0) {
            recommendations.push(`Resolve ${resourceConflicts.length} resource override conflicts`);
        }
        
        const scriptConflicts = conflicts.filter(c => c.type === UnifiedConflictType.SCRIPT_MODULE_CONFLICT);
        if (scriptConflicts.length > 0) {
            recommendations.push(`Test ${scriptConflicts.length} potentially conflicting script mods`);
        }
        
        return recommendations;
    }
    
    /**
     * Generates unique conflict ID
     */
    private static generateConflictId(): string {
        return `conflict_${++this.conflictIdCounter}_${Date.now()}`;
    }
}
