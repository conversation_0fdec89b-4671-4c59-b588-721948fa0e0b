/// <reference types="vite/client" />

export { };

declare global {
  interface Window {
    electronAPI: {
      analyzePackage: (filePath: string) => Promise<any>;
      analyzeModsFolder: (folderPath: string) => Promise<any>;
      selectModsFolder: () => Promise<any>;
      exportResults: (data: any, format: 'json' | 'csv') => Promise<any>;
      onAnalysisResult: (callback: (result: any) => void) => void;
    };
  }
}