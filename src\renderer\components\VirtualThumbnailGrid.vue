<template>
  <div class="virtual-thumbnail-grid">
    <!-- <PERSON><PERSON> -->
    <div class="grid-header">
      <div class="grid-info">
        <span class="grid-count">{{ filteredThumbnails.length }}</span>
        <span class="grid-label">items</span>
        <span v-if="hasActiveFilters" class="grid-filtered">(filtered)</span>
      </div>
      
      <div class="grid-controls">
        <!-- View Size Controls -->
        <div class="size-controls">
          <button
            @click="thumbnailSize = 'small'"
            :class="{ active: thumbnailSize === 'small' }"
            class="size-btn"
            title="Small thumbnails"
          >
            <Squares2X2Icon class="w-4 h-4" />
          </button>
          <button
            @click="thumbnailSize = 'medium'"
            :class="{ active: thumbnailSize === 'medium' }"
            class="size-btn"
            title="Medium thumbnails"
          >
            <Square3Stack3DIcon class="w-4 h-4" />
          </button>
          <button
            @click="thumbnailSize = 'large'"
            :class="{ active: thumbnailSize === 'large' }"
            class="size-btn"
            title="Large thumbnails"
          >
            <RectangleStackIcon class="w-4 h-4" />
          </button>
        </div>
        
        <!-- Sort Controls -->
        <select v-model="sortBy" class="sort-select">
          <option value="name">Sort by Name</option>
          <option value="category">Sort by Category</option>
          <option value="size">Sort by File Size</option>
          <option value="date">Sort by Date</option>
        </select>
      </div>
    </div>
    
    <!-- Loading State -->
    <div v-if="isLoading" class="grid-loading">
      <div class="loading-spinner"></div>
      <p>Extracting thumbnails...</p>
      <div class="loading-progress">
        <div class="progress-bar" :style="{ width: `${loadingProgress}%` }"></div>
      </div>
    </div>
    
    <!-- Empty State -->
    <div v-else-if="filteredThumbnails.length === 0" class="grid-empty">
      <PhotoIcon class="empty-icon" />
      <h3>No thumbnails available</h3>
      <p>No visual content found in the selected mods</p>
    </div>
    
    <!-- Virtual Scrolling Grid -->
    <div
      v-else
      ref="scrollElementRef"
      class="virtual-grid-container"
      :style="{ height: containerHeight + 'px' }"
    >
      <div
        :style="{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }"
      >
        <div
          v-for="virtualRow in virtualizer.getVirtualItems()"
          :key="virtualRow.key"
          class="virtual-row"
          :style="{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: `${virtualRow.size}px`,
            transform: `translateY(${virtualRow.start}px)`,
          }"
        >
          <div class="row-content" :class="`size-${thumbnailSize}`">
            <div
              v-for="(thumbnail, colIndex) in getRowItems(virtualRow.index)"
              :key="thumbnail?.id || `empty-${colIndex}`"
              class="thumbnail-item"
              :class="{ 'empty-slot': !thumbnail }"
              @click="thumbnail && openPreview(thumbnail)"
              @keydown.enter="thumbnail && openPreview(thumbnail)"
              :tabindex="thumbnail ? 0 : -1"
              :aria-label="thumbnail ? `${getDisplayName(thumbnail)} by ${thumbnail.category}` : undefined"
              role="gridcell"
            >
              <template v-if="thumbnail">
                <!-- Thumbnail Image -->
                <div class="thumbnail-image">
                  <img
                    :data-src="thumbnail.imageData"
                    :alt="thumbnail.modFileName"
                    :data-index="getItemIndex(virtualRow.index, colIndex)"
                    @load="onImageLoad(thumbnail)"
                    @error="onImageError(thumbnail)"
                    class="thumbnail-img"
                    :class="{
                      'loading': progressiveLoading.isLoading(getItemIndex(virtualRow.index, colIndex)),
                      'loaded': progressiveLoading.isLoaded(getItemIndex(virtualRow.index, colIndex))
                    }"
                  />
                  
                  <!-- Fallback Indicator -->
                  <div v-if="thumbnail.isFallback" class="fallback-badge">
                    <CubeIcon class="w-3 h-3" />
                  </div>
                  
                  <!-- Quality Indicator -->
                  <div v-if="thumbnail.isHighQuality" class="quality-badge">
                    <SparklesIcon class="w-3 h-3" />
                  </div>
                </div>
                
                <!-- Thumbnail Info -->
                <div class="thumbnail-info">
                  <h4 class="thumbnail-title" :title="thumbnail.modFileName">
                    {{ getDisplayName(thumbnail) }}
                  </h4>
                  <p class="thumbnail-category">
                    {{ formatCategory(thumbnail.category) }}
                    <span v-if="thumbnail.subcategory">• {{ thumbnail.subcategory }}</span>
                  </p>
                  <div class="thumbnail-meta">
                    <span class="meta-size">{{ formatFileSize(thumbnail.fileSize) }}</span>
                    <span class="meta-format">{{ thumbnail.format.toUpperCase() }}</span>
                    <span class="meta-dimensions">{{ thumbnail.width }}×{{ thumbnail.height }}</span>
                  </div>
                </div>
                
                <!-- Hover Actions -->
                <div class="thumbnail-actions">
                  <button
                    @click.stop="openPreview(thumbnail)"
                    class="action-btn preview-btn"
                    title="Preview"
                  >
                    <EyeIcon class="w-4 h-4" />
                  </button>
                  <button
                    @click.stop="showModDetails(thumbnail)"
                    class="action-btn details-btn"
                    title="Mod Details"
                  >
                    <InformationCircleIcon class="w-4 h-4" />
                  </button>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, shallowRef, shallowReactive, nextTick } from 'vue';
import { useVirtualizer } from '@tanstack/vue-virtual';
import { useProgressiveLoading } from '../composables/useProgressiveLoading';
import {
  Squares2X2Icon,
  Square3Stack3DIcon,
  RectangleStackIcon,
  PhotoIcon,
  EyeIcon,
  InformationCircleIcon,
  CubeIcon,
  SparklesIcon
} from '@heroicons/vue/24/outline';

import type { ThumbnailData } from '../../services/visual/ThumbnailExtractionService';

// Props
interface Props {
  thumbnails: ThumbnailData[];
  isLoading?: boolean;
  loadingProgress?: number;
  filters?: {
    category?: string;
    subcategory?: string;
    search?: string;
  };
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  loadingProgress: 0,
  filters: () => ({})
});

// Emits
const emit = defineEmits<{
  previewThumbnail: [thumbnail: ThumbnailData];
  showModDetails: [thumbnail: ThumbnailData];
  thumbnailLoad: [thumbnail: ThumbnailData];
  thumbnailError: [thumbnail: ThumbnailData];
}>();

// Reactive state - using shallowRef for performance with large datasets
const thumbnailSize = ref<'small' | 'medium' | 'large'>('medium');
const sortBy = ref<'name' | 'category' | 'size' | 'date'>('name');
const scrollElementRef = ref<HTMLElement>();
const containerHeight = ref(600); // Default height

// Grid configuration based on thumbnail size
const gridConfig = computed(() => {
  switch (thumbnailSize.value) {
    case 'small':
      return { columns: 8, itemHeight: 180, gap: 8 };
    case 'medium':
      return { columns: 6, itemHeight: 220, gap: 16 };
    case 'large':
      return { columns: 4, itemHeight: 280, gap: 24 };
    default:
      return { columns: 6, itemHeight: 220, gap: 16 };
  }
});

// Computed properties using shallow reactivity for performance
const hasActiveFilters = computed(() => {
  return !!(props.filters.category || props.filters.subcategory || props.filters.search);
});

// Use shallowRef for large arrays to optimize performance
const filteredThumbnails = shallowRef<ThumbnailData[]>([]);

// Watch for changes and update filtered thumbnails
watch(
  [() => props.thumbnails, () => props.filters, sortBy],
  () => {
    let filtered = [...props.thumbnails];

    // Apply category filter
    if (props.filters.category) {
      filtered = filtered.filter(t => t.category === props.filters.category);
    }

    // Apply subcategory filter
    if (props.filters.subcategory) {
      filtered = filtered.filter(t => t.subcategory === props.filters.subcategory);
    }

    // Apply search filter
    if (props.filters.search) {
      const search = props.filters.search.toLowerCase();
      filtered = filtered.filter(t =>
        t.modFileName.toLowerCase().includes(search) ||
        t.category.toLowerCase().includes(search) ||
        (t.subcategory && t.subcategory.toLowerCase().includes(search))
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy.value) {
        case 'name':
          return a.modFileName.localeCompare(b.modFileName);
        case 'category':
          return a.category.localeCompare(b.category);
        case 'size':
          return b.fileSize - a.fileSize;
        case 'date':
          return a.modFileName.localeCompare(b.modFileName); // Fallback to name
        default:
          return 0;
      }
    });

    filteredThumbnails.value = filtered;
  },
  { immediate: true, deep: true }
);

// Calculate total rows needed for virtual scrolling
const totalRows = computed(() => {
  return Math.ceil(filteredThumbnails.value.length / gridConfig.value.columns);
});

// Virtual scrolling setup using TanStack Virtual
const virtualizer = useVirtualizer({
  count: totalRows,
  getScrollElement: () => scrollElementRef.value,
  estimateSize: () => gridConfig.value.itemHeight,
  overscan: 5, // Render 5 extra items above and below visible area
  gap: gridConfig.value.gap,
});

// Progressive loading setup
const progressiveLoading = useProgressiveLoading({
  rootMargin: '100px', // Start loading images 100px before they enter viewport
  threshold: 0.1,
  loadingDelay: 50, // Reduced delay for faster loading
  batchSize: 8, // Load more images per batch for better performance
  enablePrefetch: true,
  prefetchDistance: 300
});

// Get items for a specific row
const getRowItems = (rowIndex: number): (ThumbnailData | null)[] => {
  const startIndex = rowIndex * gridConfig.value.columns;
  const endIndex = startIndex + gridConfig.value.columns;
  const rowItems: (ThumbnailData | null)[] = [];

  for (let i = startIndex; i < endIndex; i++) {
    rowItems.push(filteredThumbnails.value[i] || null);
  }

  return rowItems;
};

// Get global item index for progressive loading
const getItemIndex = (rowIndex: number, colIndex: number): number => {
  return rowIndex * gridConfig.value.columns + colIndex;
};

// Methods
const openPreview = (thumbnail: ThumbnailData) => {
  emit('previewThumbnail', thumbnail);
};

const showModDetails = (thumbnail: ThumbnailData) => {
  emit('showModDetails', thumbnail);
};

const onImageLoad = (thumbnail: ThumbnailData) => {
  emit('thumbnailLoad', thumbnail);
};

const onImageError = (thumbnail: ThumbnailData) => {
  emit('thumbnailError', thumbnail);
};

const getDisplayName = (thumbnail: ThumbnailData): string => {
  return thumbnail.modFileName
    .replace(/\.(package|ts4script)$/i, '')
    .replace(/[_-]/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
};

const formatCategory = (category: string): string => {
  return category
    .replace(/[_-]/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// Set up responsive container height and progressive loading
onMounted(() => {
  if (scrollElementRef.value) {
    // Set initial height based on viewport
    const viewportHeight = window.innerHeight;
    containerHeight.value = Math.min(600, viewportHeight - 200); // Leave space for header/controls
  }

  // Set up progressive loading
  nextTick(() => {
    progressiveLoading.setTotalItems(filteredThumbnails.value.length);

    // Observe all thumbnail images for progressive loading
    const images = scrollElementRef.value?.querySelectorAll('img[data-src]');
    images?.forEach(img => progressiveLoading.observe(img));
  });
});

// Watch for changes in filtered thumbnails to update progressive loading
watch(filteredThumbnails, (newThumbnails) => {
  progressiveLoading.reset();
  progressiveLoading.setTotalItems(newThumbnails.length);

  nextTick(() => {
    const images = scrollElementRef.value?.querySelectorAll('img[data-src]');
    images?.forEach(img => progressiveLoading.observe(img));
  });
});
</script>

<style scoped>
.virtual-thumbnail-grid {
  @apply w-full;
}

.grid-header {
  @apply flex items-center justify-between mb-6 p-4 bg-surface rounded-lg border;
}

.grid-info {
  @apply flex items-center gap-2 text-sm;
}

.grid-count {
  @apply font-semibold text-primary;
}

.grid-label {
  @apply text-muted;
}

.grid-filtered {
  @apply text-xs text-warning bg-warning/10 px-2 py-1 rounded;
}

.grid-controls {
  @apply flex items-center gap-4;
}

.size-controls {
  @apply flex items-center gap-1 p-1 bg-background rounded border;
}

.size-btn {
  @apply p-2 rounded transition-colors;
}

.size-btn:hover {
  @apply bg-surface;
}

.size-btn.active {
  @apply bg-primary text-primary-foreground;
}

.sort-select {
  @apply px-3 py-2 border rounded bg-background text-sm;
}

.grid-loading {
  @apply flex flex-col items-center justify-center py-12 text-center;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mb-4;
}

.loading-progress {
  @apply w-64 h-2 bg-surface rounded-full overflow-hidden mt-4;
}

.progress-bar {
  @apply h-full bg-primary transition-all duration-300;
}

.grid-empty {
  @apply flex flex-col items-center justify-center py-12 text-center;
}

.empty-icon {
  @apply w-16 h-16 text-muted mb-4;
}

/* Virtual scrolling container */
.virtual-grid-container {
  @apply w-full overflow-auto border rounded-lg bg-background;
}

.virtual-row {
  @apply w-full;
}

.row-content {
  @apply grid gap-4 p-4;
}

.row-content.size-small {
  @apply grid-cols-8 gap-2;
}

.row-content.size-medium {
  @apply grid-cols-6 gap-4;
}

.row-content.size-large {
  @apply grid-cols-4 gap-6;
}

.thumbnail-item {
  @apply relative bg-surface border overflow-hidden cursor-pointer transition-all duration-200 group;
  border-radius: 10px; /* Consistent rounded corners */
  aspect-ratio: 1; /* Perfect square aspect ratio */
}

.thumbnail-item:hover {
  @apply border-primary shadow-lg transform scale-105;
}

.thumbnail-item:focus {
  @apply outline-none ring-2 ring-primary ring-offset-2;
}

.thumbnail-item.empty-slot {
  @apply invisible;
}

.thumbnail-image {
  @apply relative aspect-square overflow-hidden;
}

.thumbnail-img {
  @apply w-full h-full object-cover transition-opacity duration-300;
  opacity: 0;
}

.thumbnail-img.loading {
  @apply bg-surface animate-pulse;
}

.thumbnail-img.loaded {
  opacity: 1;
}

.thumbnail-img.error {
  @apply bg-destructive/10;
}

.fallback-badge {
  @apply absolute top-2 left-2 bg-warning text-warning-foreground p-1 rounded;
}

.quality-badge {
  @apply absolute top-2 right-2 bg-success text-success-foreground p-1 rounded;
}

.thumbnail-info {
  @apply p-3;
}

.thumbnail-title {
  @apply font-medium text-sm mb-1 truncate;
}

.thumbnail-category {
  @apply text-xs text-muted mb-2;
}

.thumbnail-meta {
  @apply flex items-center gap-2 text-xs text-muted;
}

.meta-size,
.meta-format,
.meta-dimensions {
  @apply px-1 py-0.5 bg-background rounded;
}

.thumbnail-actions {
  @apply absolute inset-0 bg-black/50 flex items-center justify-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity;
}

.action-btn {
  @apply p-2 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .row-content.size-small {
    @apply grid-cols-6;
  }

  .row-content.size-medium {
    @apply grid-cols-4;
  }

  .row-content.size-large {
    @apply grid-cols-3;
  }
}

@media (max-width: 768px) {
  .row-content.size-small {
    @apply grid-cols-4;
  }

  .row-content.size-medium {
    @apply grid-cols-3;
  }

  .row-content.size-large {
    @apply grid-cols-2;
  }

  .grid-header {
    @apply flex-col gap-4;
  }
}

@media (max-width: 480px) {
  .row-content.size-small {
    @apply grid-cols-3;
  }

  .row-content.size-medium {
    @apply grid-cols-2;
  }

  .row-content.size-large {
    @apply grid-cols-1;
  }
}
</style>
