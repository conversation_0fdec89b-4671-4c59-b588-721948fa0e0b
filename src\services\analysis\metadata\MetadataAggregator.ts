/**
 * Multi-Source Metadata Aggregator
 * 
 * Combines metadata from all sources with confidence scoring and conflict resolution.
 * Implements intelligent merging strategies and quality assessment.
 */

import { DEFAULT_METADATA_CONFIG, type MetadataExtractionConfig } from '../../../config/metadata-extraction.config';
import type { FilenameMetadata } from './FilenameMetadataExtractor';
import type { StringTableMetadata } from './CustomStringTableParser';
import type { TuningMetadata } from './TuningMetadataExtractor';
import type { SimDataMetadata } from './SimDataMetadataExtractor';
import type { ManifestMetadata } from './ManifestDetector';

export type MetadataSource = FilenameMetadata | StringTableMetadata | TuningMetadata | SimDataMetadata | ManifestMetadata;

export interface AggregatedMetadata {
    readonly author?: string;
    readonly modName?: string;
    readonly version?: string;
    readonly description?: string;
    readonly downloadUrl?: string;
    readonly requirements?: readonly string[];
    readonly installationGuidelines?: string;
    
    // Confidence and quality metrics
    readonly overallConfidence: number;
    readonly sourceCount: number;
    readonly hasConflicts: boolean;
    readonly qualityScore: number;
    
    // Source breakdown
    readonly sources: readonly MetadataSource[];
    readonly sourceTypes: readonly string[];
    readonly primarySource: string;
    
    // Processing metrics
    readonly totalProcessingTime: number;
    readonly aggregationTime: number;
}

export interface ConflictResolution {
    readonly field: keyof AggregatedMetadata;
    readonly conflictingSources: readonly MetadataSource[];
    readonly chosenValue: string;
    readonly chosenSource: string;
    readonly reason: string;
}

/**
 * Metadata aggregation and conflict resolution system
 */
export class MetadataAggregator {
    private readonly config: MetadataExtractionConfig;
    
    constructor(config: Partial<MetadataExtractionConfig> = {}) {
        this.config = {
            ...DEFAULT_METADATA_CONFIG,
            ...config
        };
    }
    
    /**
     * Aggregates metadata from multiple sources
     */
    async aggregateMetadata(sources: MetadataSource[]): Promise<AggregatedMetadata> {
        const startTime = performance.now();
        
        if (sources.length === 0) {
            return this.createEmptyResult(startTime);
        }
        
        // Sort sources by confidence and priority
        const sortedSources = this.sortSourcesByPriority(sources);
        
        // Aggregate metadata fields
        const aggregated = this.aggregateFields(sortedSources);
        
        // Detect and resolve conflicts
        const conflicts = this.detectConflicts(sortedSources);
        const resolved = this.resolveConflicts(aggregated, conflicts);
        
        // Calculate quality metrics
        const qualityMetrics = this.calculateQualityMetrics(resolved, sortedSources);
        
        const aggregationTime = performance.now() - startTime;
        const totalProcessingTime = sources.reduce((sum, source) => sum + source.processingTime, 0);
        
        return {
            ...resolved,
            overallConfidence: qualityMetrics.overallConfidence,
            sourceCount: sources.length,
            hasConflicts: conflicts.length > 0,
            qualityScore: qualityMetrics.qualityScore,
            sources: sortedSources,
            sourceTypes: [...new Set(sources.map(s => s.source))],
            primarySource: sortedSources[0]?.source || 'none',
            totalProcessingTime,
            aggregationTime
        };
    }
    
    /**
     * Sorts sources by priority and confidence
     */
    private sortSourcesByPriority(sources: MetadataSource[]): MetadataSource[] {
        return [...sources].sort((a, b) => {
            // First sort by source type priority
            const priorityA = this.getSourcePriority(a.source);
            const priorityB = this.getSourcePriority(b.source);
            
            if (priorityA !== priorityB) {
                return priorityB - priorityA; // Higher priority first
            }
            
            // Then by confidence
            return b.confidence - a.confidence;
        });
    }
    
    /**
     * Gets priority for source type
     */
    private getSourcePriority(sourceType: string): number {
        const weights = this.config.confidence.weights;
        return weights[sourceType as keyof typeof weights] || 0;
    }
    
    /**
     * Aggregates fields from sorted sources
     */
    private aggregateFields(sources: MetadataSource[]): {
        author?: string;
        modName?: string;
        version?: string;
        description?: string;
        downloadUrl?: string;
        requirements?: string[];
        installationGuidelines?: string;
    } {
        const result: {
            author?: string;
            modName?: string;
            version?: string;
            description?: string;
            downloadUrl?: string;
            requirements?: string[];
            installationGuidelines?: string;
        } = {};
        
        // Take the first (highest priority/confidence) value for each field
        for (const source of sources) {
            if (!result.author && source.author) {
                result.author = source.author;
            }
            if (!result.modName && source.modName) {
                result.modName = source.modName;
            }
            if (!result.version && source.version) {
                result.version = source.version;
            }
            if (!result.description && source.description) {
                result.description = source.description;
            }
            
            // Manifest-specific fields
            if ('downloadUrl' in source && !result.downloadUrl && source.downloadUrl) {
                result.downloadUrl = source.downloadUrl;
            }
            if ('requirements' in source && !result.requirements && source.requirements) {
                result.requirements = [...source.requirements];
            }
            if ('installationGuidelines' in source && !result.installationGuidelines && source.installationGuidelines) {
                result.installationGuidelines = source.installationGuidelines;
            }
        }
        
        return result;
    }
    
    /**
     * Detects conflicts between sources
     */
    private detectConflicts(sources: MetadataSource[]): ConflictResolution[] {
        const conflicts: ConflictResolution[] = [];
        const fields: Array<keyof MetadataSource> = ['author', 'modName', 'version', 'description'];
        
        for (const field of fields) {
            const valuesWithSources = sources
                .filter(source => source[field])
                .map(source => ({ value: String(source[field]), source }));

            if (valuesWithSources.length > 1) {
                const uniqueValues = [...new Set(valuesWithSources.map(v => v.value.toLowerCase()))];
                
                if (uniqueValues.length > 1) {
                    // We have a conflict
                    const conflictingSources = valuesWithSources.map(v => v.source);
                    const chosenSource = conflictingSources[0]; // Highest priority
                    const chosenValue = valuesWithSources[0].value;
                    
                    conflicts.push({
                        field: field as keyof AggregatedMetadata,
                        conflictingSources,
                        chosenValue,
                        chosenSource: chosenSource.source,
                        reason: `Chose highest priority source (${chosenSource.source})`
                    });
                }
            }
        }
        
        return conflicts;
    }
    
    /**
     * Resolves conflicts using priority rules
     */
    private resolveConflicts(
        aggregated: ReturnType<typeof this.aggregateFields>,
        conflicts: ConflictResolution[]
    ): ReturnType<typeof this.aggregateFields> {
        // For now, we already chose the highest priority values in aggregateFields
        // This method could implement more sophisticated conflict resolution
        return aggregated;
    }
    
    /**
     * Calculates quality metrics
     */
    private calculateQualityMetrics(
        metadata: ReturnType<typeof this.aggregateFields>,
        sources: MetadataSource[]
    ): { overallConfidence: number; qualityScore: number } {
        if (sources.length === 0) {
            return { overallConfidence: 0, qualityScore: 0 };
        }
        
        // Calculate weighted confidence
        const totalWeight = sources.reduce((sum, source) => {
            const weight = this.config.confidence.weights[source.source as keyof typeof this.config.confidence.weights] || 1;
            return sum + weight;
        }, 0);
        
        const weightedConfidence = sources.reduce((sum, source) => {
            const weight = this.config.confidence.weights[source.source as keyof typeof this.config.confidence.weights] || 1;
            return sum + (source.confidence * weight);
        }, 0);
        
        const overallConfidence = totalWeight > 0 ? weightedConfidence / totalWeight : 0;
        
        // Calculate quality score based on completeness and confidence
        let qualityScore = overallConfidence * 0.6; // Base on confidence
        
        // Boost for completeness
        const fieldCount = [metadata.author, metadata.modName, metadata.version, metadata.description].filter(Boolean).length;
        qualityScore += (fieldCount / 4) * 30; // Up to 30 points for completeness
        
        // Boost for multiple sources
        if (sources.length > 1) {
            qualityScore += Math.min(sources.length * 2, 10); // Up to 10 points for multiple sources
        }
        
        return {
            overallConfidence: Math.min(overallConfidence, 100),
            qualityScore: Math.min(qualityScore, 100)
        };
    }
    
    /**
     * Creates empty result for no sources
     */
    private createEmptyResult(startTime: number): AggregatedMetadata {
        return {
            overallConfidence: 0,
            sourceCount: 0,
            hasConflicts: false,
            qualityScore: 0,
            sources: [],
            sourceTypes: [],
            primarySource: 'none',
            totalProcessingTime: 0,
            aggregationTime: performance.now() - startTime
        };
    }
    
    /**
     * Validates aggregated metadata quality
     */
    validateMetadata(metadata: AggregatedMetadata): {
        isValid: boolean;
        issues: string[];
        recommendations: string[];
    } {
        const issues: string[] = [];
        const recommendations: string[] = [];
        
        // Check minimum confidence
        if (metadata.overallConfidence < this.config.confidence.minimumConfidence) {
            issues.push(`Low confidence: ${metadata.overallConfidence}% (minimum: ${this.config.confidence.minimumConfidence}%)`);
        }
        
        // Check for essential fields
        if (!metadata.modName && !metadata.author) {
            issues.push('Missing both mod name and author');
            recommendations.push('Consider improving filename patterns or adding manifest');
        }
        
        // Check for conflicts
        if (metadata.hasConflicts) {
            recommendations.push('Review conflicting sources for accuracy');
        }
        
        // Check source diversity
        if (metadata.sourceCount === 1) {
            recommendations.push('Consider adding manifest or improving metadata in mod files');
        }
        
        return {
            isValid: issues.length === 0 && metadata.overallConfidence >= this.config.confidence.minimumConfidence,
            issues,
            recommendations
        };
    }
}
