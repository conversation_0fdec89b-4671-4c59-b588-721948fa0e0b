#!/usr/bin/env tsx

/**
 * Test script to verify UI multiple thumbnail support
 * Tests the enhanced ModData structure and thumbnail extraction
 */

import * as fs from 'fs';
import * as path from 'path';
import { ThumbnailExtractionService } from './src/services/visual/ThumbnailExtractionService';
import type { ModData, VisualModData } from './src/types/ModData';

const TIFFANY_PACKAGE_PATH = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods\\KiaraZurk_TiffanyHairstyle\\KiaraZurk_TiffanyHairstyle.package';

/**
 * Simulate the enhanced ModData creation process
 */
function createEnhancedModData(
    fileName: string, 
    filePath: string, 
    thumbnailResult: any
): VisualModData {
    const baseModData: ModData = {
        fileName,
        filePath,
        fileExtension: '.package',
        fileSize: fs.statSync(filePath).size,
        processingTime: 0,
        category: 'cas_cc'
    };

    if (thumbnailResult.success && thumbnailResult.thumbnails.length > 0) {
        const primaryThumbnail = thumbnailResult.thumbnails[0];
        const variations = thumbnailResult.thumbnails.slice(1);

        return {
            ...baseModData,
            // Legacy fields for backward compatibility
            thumbnailUrl: primaryThumbnail.imageData,
            thumbnailData: primaryThumbnail.imageData,
            // NEW: Enhanced thumbnail support
            thumbnails: thumbnailResult.thumbnails,
            primaryThumbnail: primaryThumbnail,
            thumbnailVariations: variations,
            hasMultipleVariations: thumbnailResult.thumbnails.length > 1,
            // Visual browser fields
            visualTags: ['hair', 'cas', 'female'],
            visualCategory: 'Hair',
            visualSubcategory: 'Long Hair',
            thumbnailGenerationStatus: 'completed'
        };
    }

    return {
        ...baseModData,
        thumbnails: [],
        thumbnailVariations: [],
        hasMultipleVariations: false,
        visualTags: [],
        visualCategory: 'Unknown',
        thumbnailGenerationStatus: 'failed'
    };
}

async function testUIMultipleThumbnails() {
    console.log('🧪 Testing UI Multiple Thumbnail Support...\n');
    
    try {
        if (!fs.existsSync(TIFFANY_PACKAGE_PATH)) {
            console.log(`❌ Package file not found: ${TIFFANY_PACKAGE_PATH}`);
            return;
        }

        // Read the file
        const buffer = fs.readFileSync(TIFFANY_PACKAGE_PATH);
        console.log(`📦 Testing with: ${path.basename(TIFFANY_PACKAGE_PATH)}`);
        console.log(`📏 File size: ${(buffer.length / 1024).toFixed(2)} KB`);
        
        // Extract thumbnails with new settings (like ModDashboard)
        console.log('\n🔍 Extracting thumbnails with UI settings...');
        const result = await ThumbnailExtractionService.extractThumbnails(
            buffer,
            path.basename(TIFFANY_PACKAGE_PATH),
            {
                maxThumbnails: 25, // Allow up to 25 thumbnails for hair color variations
                preferredFormat: 'webp',
                maxWidth: 256,
                maxHeight: 256,
                prioritizeCasThumbnails: false, // Prioritize RLE2 for color variations
                generateFallbacks: true
            }
        );
        
        console.log('\n📊 Extraction Results:');
        console.log(`✅ Success: ${result.success}`);
        console.log(`🖼️ Thumbnails extracted: ${result.thumbnailsExtracted}`);
        console.log(`📈 Total images found: ${result.totalImagesFound}`);
        console.log(`⏱️ Processing time: ${result.processingTime.toFixed(2)}ms`);
        console.log(`⚡ Average time per thumbnail: ${(result.processingTime / result.thumbnailsExtracted).toFixed(2)}ms`);
        
        if (result.errors.length > 0) {
            console.log('\n❌ Errors:');
            result.errors.forEach(error => console.log(`  - ${error}`));
        }
        
        // Create enhanced ModData structure
        console.log('\n🔧 Creating enhanced ModData structure...');
        const enhancedModData = createEnhancedModData(
            path.basename(TIFFANY_PACKAGE_PATH),
            TIFFANY_PACKAGE_PATH,
            result
        );
        
        console.log('\n📋 Enhanced ModData Summary:');
        console.log(`  File Name: ${enhancedModData.fileName}`);
        console.log(`  Has Multiple Variations: ${enhancedModData.hasMultipleVariations}`);
        console.log(`  Total Thumbnails: ${enhancedModData.thumbnails?.length || 0}`);
        console.log(`  Primary Thumbnail: ${enhancedModData.primaryThumbnail ? 'Yes' : 'No'}`);
        console.log(`  Variations Count: ${enhancedModData.thumbnailVariations?.length || 0}`);
        console.log(`  Visual Category: ${enhancedModData.visualCategory}`);
        console.log(`  Thumbnail Status: ${enhancedModData.thumbnailGenerationStatus}`);
        
        // Analyze thumbnail types
        if (enhancedModData.thumbnails && enhancedModData.thumbnails.length > 0) {
            console.log('\n🎨 Thumbnail Analysis:');
            const thumbnailsByType = new Map<string, number>();
            const thumbnailsByMethod = new Map<string, number>();
            
            enhancedModData.thumbnails.forEach(thumb => {
                const typeCount = thumbnailsByType.get(thumb.resourceType) || 0;
                thumbnailsByType.set(thumb.resourceType, typeCount + 1);
                
                const methodCount = thumbnailsByMethod.get(thumb.extractionMethod) || 0;
                thumbnailsByMethod.set(thumb.extractionMethod, methodCount + 1);
            });
            
            console.log('  By Resource Type:');
            thumbnailsByType.forEach((count, type) => {
                console.log(`    ${type}: ${count} thumbnails`);
            });
            
            console.log('  By Extraction Method:');
            thumbnailsByMethod.forEach((count, method) => {
                console.log(`    ${method}: ${count} thumbnails`);
            });
            
            // Show first few thumbnails
            console.log('\n🖼️ Sample Thumbnails:');
            enhancedModData.thumbnails.slice(0, 3).forEach((thumb, index) => {
                console.log(`  ${index + 1}. ${thumb.resourceType} (${thumb.extractionMethod})`);
                console.log(`     Size: ${thumb.width}x${thumb.height}, Quality: ${thumb.isHighQuality ? 'High' : 'Low'}`);
                console.log(`     Data length: ${thumb.imageData.length} chars`);
            });
        }
        
        // UI Component Simulation
        console.log('\n🎭 UI Component Simulation:');
        console.log('  ModThumbnailCard would display:');
        console.log(`    - Primary thumbnail: ${enhancedModData.primaryThumbnail?.resourceType || 'None'}`);
        console.log(`    - Variation count badge: ${enhancedModData.thumbnailVariations?.length || 0}`);
        console.log(`    - Show variations toggle: ${enhancedModData.hasMultipleVariations ? 'Yes' : 'No'}`);
        
        if (enhancedModData.hasMultipleVariations) {
            console.log('  ThumbnailVariationDisplay would show:');
            console.log(`    - Grid with ${Math.min(6, enhancedModData.thumbnailVariations?.length || 0)} visible variations`);
            console.log(`    - "Show more" button: ${(enhancedModData.thumbnailVariations?.length || 0) > 6 ? 'Yes' : 'No'}`);
        }
        
        console.log('\n🎉 UI Multiple Thumbnail Test Completed Successfully!');
        console.log(`✨ Ready to display ${enhancedModData.thumbnails?.length || 0} hair color variations in the UI!`);
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Run the test
testUIMultipleThumbnails().catch(console.error);
