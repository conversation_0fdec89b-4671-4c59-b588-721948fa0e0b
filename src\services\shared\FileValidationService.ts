/**
 * Shared File Validation Service
 * 
 * Consolidates file validation logic to eliminate duplication across services.
 * Provides standardized validation for packages, scripts, and general files.
 */

import * as fs from 'fs';
import { PackageParserService } from './PackageParserService';
import { UnifiedErrorHandler, UnifiedErrorCategory, UnifiedErrorSeverity, type UnifiedErrorInfo } from './UnifiedErrorHandler';

export interface BasicValidationResult {
    isValid: boolean;
    fileSize: number;
    errors: UnifiedErrorInfo[];
    warnings: UnifiedErrorInfo[];
}

export interface PackageValidationResult extends BasicValidationResult {
    hasValidHeader: boolean;
    resourceCount: number;
    packageVersion: number;
    canParse: boolean;
}

export interface ScriptValidationResult extends BasicValidationResult {
    hasPythonSyntax: boolean;
    hasImports: boolean;
    encoding: string;
    lineCount: number;
}

// Legacy interfaces - deprecated in favor of UnifiedErrorInfo
export interface ValidationError {
    code: string;
    severity: 'critical' | 'high' | 'medium' | 'low';
    message: string;
    suggestion?: string;
}

export interface ValidationWarning {
    code: string;
    message: string;
    impact: string;
}

/**
 * Centralized file validation service
 */
export class FileValidationService {
    private static readonly MIN_PACKAGE_SIZE = 96; // DBPF header minimum
    private static readonly MIN_SCRIPT_SIZE = 10;
    private static readonly MAX_REASONABLE_SIZE = 100 * 1024 * 1024; // 100MB

    /**
     * Converts legacy ValidationError to UnifiedErrorInfo
     */
    private static convertLegacyError(legacyError: ValidationError, context: string, filePath?: string): UnifiedErrorInfo {
        return UnifiedErrorHandler.createError(
            new Error(legacyError.message),
            context,
            filePath || 'unknown',
            UnifiedErrorCategory.FILE_VALIDATION
        );
    }

    /**
     * Converts legacy ValidationWarning to UnifiedErrorInfo
     */
    private static convertLegacyWarning(legacyWarning: ValidationWarning, context: string, filePath?: string): UnifiedErrorInfo {
        const error = UnifiedErrorHandler.createError(
            new Error(legacyWarning.message),
            context,
            filePath || 'unknown',
            UnifiedErrorCategory.FILE_VALIDATION
        );
        // Override severity to be lower for warnings
        return { ...error, severity: UnifiedErrorSeverity.LOW };
    }

    /**
     * Validates basic file properties
     */
    public static validateFileBasics(buffer: Buffer, fileName: string): BasicValidationResult {
        const result: BasicValidationResult = {
            isValid: true,
            fileSize: buffer.length,
            errors: [],
            warnings: []
        };

        // Check if file is empty
        if (buffer.length === 0) {
            const error = UnifiedErrorHandler.createError(
                new Error('File is empty'),
                'FileValidationService.validateFileBasics',
                fileName,
                UnifiedErrorCategory.FILE_VALIDATION
            );
            result.errors.push(error);
            result.isValid = false;
            return result;
        }

        // Check for suspiciously small files
        if (buffer.length < 100) {
            const error = UnifiedErrorHandler.createError(
                new Error(`File is suspiciously small (${buffer.length} bytes)`),
                'FileValidationService.validateFileBasics',
                fileName,
                UnifiedErrorCategory.FILE_VALIDATION
            );
            result.errors.push(error);
            result.isValid = false;
        }

        // Check for unreasonably large files
        if (buffer.length > this.MAX_REASONABLE_SIZE) {
            const warning = this.convertLegacyWarning({
                code: 'FILE_VERY_LARGE',
                message: `File is very large (${Math.round(buffer.length / 1024 / 1024)}MB)`,
                impact: 'May cause performance issues'
            }, 'FileValidationService.validateFileBasics', fileName);
            result.warnings.push(warning);
        }

        // Check for null byte sequences (corruption indicator)
        if (this.hasLargeNullSequences(buffer)) {
            result.errors.push({
                code: 'NULL_BYTE_CORRUPTION',
                severity: 'high',
                message: 'File contains large sequences of null bytes',
                suggestion: 'File may be corrupted, try re-downloading'
            });
            result.isValid = false;
        }

        // Check filename for common issues
        this.validateFileName(fileName, result);

        return result;
    }

    /**
     * Validates package file structure
     */
    public static validatePackageStructure(buffer: Buffer, fileName: string): PackageValidationResult {
        const basicResult = this.validateFileBasics(buffer, fileName);
        
        const result: PackageValidationResult = {
            ...basicResult,
            hasValidHeader: false,
            resourceCount: 0,
            packageVersion: 0,
            canParse: false
        };

        // Skip package validation if basic validation failed critically
        if (!basicResult.isValid && basicResult.errors.some(e => e.severity === 'critical')) {
            return result;
        }

        // Check minimum size for package
        if (buffer.length < this.MIN_PACKAGE_SIZE) {
            result.errors.push({
                code: 'PACKAGE_TOO_SMALL',
                severity: 'critical',
                message: `Package file too small (${buffer.length} bytes, minimum ${this.MIN_PACKAGE_SIZE})`,
                suggestion: 'File may be incomplete or corrupted'
            });
            result.isValid = false;
            return result;
        }

        // Validate DBPF header
        try {
            const headerValidation = this.validateDBPFHeader(buffer);
            result.hasValidHeader = headerValidation.isValid;
            result.packageVersion = headerValidation.version;

            if (!headerValidation.isValid) {
                result.errors.push(...headerValidation.errors);
                result.isValid = false;
            }
        } catch (error) {
            result.errors.push({
                code: 'HEADER_READ_ERROR',
                severity: 'critical',
                message: `Failed to read package header: ${error.message}`,
                suggestion: 'File may be corrupted or not a valid package'
            });
            result.isValid = false;
            return result;
        }

        // Try to parse package structure
        try {
            const packageStructure = PackageParserService.validatePackageStructure(buffer);
            result.canParse = packageStructure.isValid;
            result.resourceCount = packageStructure.resourceCount;

            if (!packageStructure.isValid) {
                for (const error of packageStructure.errors) {
                    result.errors.push({
                        code: 'PACKAGE_STRUCTURE_ERROR',
                        severity: 'high',
                        message: error,
                        suggestion: 'Package structure is invalid'
                    });
                }
                result.isValid = false;
            }

            // Warn about empty packages
            if (packageStructure.resourceCount === 0) {
                result.warnings.push({
                    code: 'EMPTY_PACKAGE',
                    message: 'Package contains no resources',
                    impact: 'Mod will have no effect in-game'
                });
            }

        } catch (error) {
            result.errors.push({
                code: 'PACKAGE_PARSE_ERROR',
                severity: 'high',
                message: `Failed to parse package: ${error.message}`,
                suggestion: 'Package may be corrupted or use unsupported format'
            });
            result.isValid = false;
        }

        return result;
    }

    /**
     * Validates script file content
     */
    public static validateScriptFile(buffer: Buffer, fileName: string): ScriptValidationResult {
        const basicResult = this.validateFileBasics(buffer, fileName);
        
        const result: ScriptValidationResult = {
            ...basicResult,
            hasPythonSyntax: false,
            hasImports: false,
            encoding: 'unknown',
            lineCount: 0
        };

        // Skip script validation if basic validation failed critically
        if (!basicResult.isValid && basicResult.errors.some(e => e.severity === 'critical')) {
            return result;
        }

        try {
            // Try to decode as UTF-8 first
            let content: string;
            try {
                content = buffer.toString('utf8');
                result.encoding = 'utf8';
            } catch (error) {
                // Try other encodings
                try {
                    content = buffer.toString('latin1');
                    result.encoding = 'latin1';
                } catch (error) {
                    result.errors.push({
                        code: 'ENCODING_ERROR',
                        severity: 'high',
                        message: 'Cannot decode script file',
                        suggestion: 'File may be corrupted or use unsupported encoding'
                    });
                    result.isValid = false;
                    return result;
                }
            }

            result.lineCount = content.split('\n').length;

            // Check for Python syntax indicators
            result.hasPythonSyntax = this.hasPythonSyntax(content);
            result.hasImports = this.hasImportStatements(content);

            // Check for common syntax errors
            if (content.includes('SyntaxError') || content.includes('IndentationError')) {
                result.errors.push({
                    code: 'PYTHON_SYNTAX_ERROR',
                    severity: 'high',
                    message: 'Script contains Python syntax errors',
                    suggestion: 'Contact mod creator for a fixed version'
                });
                result.isValid = false;
            }

            // Check for incomplete files
            if (!this.hasValidScriptEnding(content)) {
                result.warnings.push({
                    code: 'INCOMPLETE_SCRIPT',
                    message: 'Script file may be incomplete',
                    impact: 'Script may not function correctly'
                });
            }

            // Check for binary content in script
            if (this.hasBinaryContent(buffer)) {
                result.errors.push({
                    code: 'BINARY_IN_SCRIPT',
                    severity: 'medium',
                    message: 'Script file contains binary data',
                    suggestion: 'File may be corrupted or not a text script'
                });
            }

        } catch (error) {
            result.errors.push({
                code: 'SCRIPT_ANALYSIS_ERROR',
                severity: 'medium',
                message: `Failed to analyze script: ${error.message}`,
                suggestion: 'Script may have encoding or format issues'
            });
        }

        return result;
    }

    /**
     * Validates DBPF header
     */
    private static validateDBPFHeader(buffer: Buffer): {
        isValid: boolean;
        version: number;
        errors: ValidationError[];
    } {
        const result = {
            isValid: true,
            version: 0,
            errors: [] as ValidationError[]
        };

        // Check DBPF signature
        const signature = buffer.toString('ascii', 0, 4);
        if (signature !== 'DBPF') {
            result.errors.push({
                code: 'INVALID_SIGNATURE',
                severity: 'critical',
                message: `Invalid package signature: "${signature}" (expected "DBPF")`,
                suggestion: 'File is not a valid Sims 4 package'
            });
            result.isValid = false;
            return result;
        }

        // Read version
        try {
            result.version = buffer.readUInt32LE(4);
            
            // Check for supported versions
            if (result.version < 2 || result.version > 3) {
                result.errors.push({
                    code: 'UNSUPPORTED_VERSION',
                    severity: 'medium',
                    message: `Package version ${result.version} may not be fully supported`,
                    suggestion: 'Package may be from older or newer game version'
                });
            }
        } catch (error) {
            result.errors.push({
                code: 'VERSION_READ_ERROR',
                severity: 'high',
                message: 'Cannot read package version',
                suggestion: 'Package header may be corrupted'
            });
            result.isValid = false;
        }

        return result;
    }

    /**
     * Checks for large null byte sequences (corruption indicator)
     */
    private static hasLargeNullSequences(buffer: Buffer): boolean {
        let nullCount = 0;
        const maxNullSequence = 100;

        for (let i = 0; i < buffer.length; i++) {
            if (buffer[i] === 0) {
                nullCount++;
                if (nullCount > maxNullSequence) {
                    return true;
                }
            } else {
                nullCount = 0;
            }
        }

        return false;
    }

    /**
     * Validates filename for common issues
     */
    private static validateFileName(fileName: string, result: BasicValidationResult): void {
        // Check for invalid characters
        const invalidChars = /[<>:"|?*]/;
        if (invalidChars.test(fileName)) {
            result.warnings.push({
                code: 'INVALID_FILENAME_CHARS',
                message: 'Filename contains invalid characters',
                impact: 'May cause issues on some systems'
            });
        }

        // Check for very long filenames
        if (fileName.length > 255) {
            result.warnings.push({
                code: 'FILENAME_TOO_LONG',
                message: 'Filename is very long',
                impact: 'May cause issues on some systems'
            });
        }

        // Check for proper extension
        const extension = fileName.toLowerCase().split('.').pop();
        if (!['package', 'ts4script'].includes(extension || '')) {
            result.warnings.push({
                code: 'UNEXPECTED_EXTENSION',
                message: `Unexpected file extension: .${extension}`,
                impact: 'File may not be recognized by the game'
            });
        }
    }

    /**
     * Checks if content has Python syntax
     */
    private static hasPythonSyntax(content: string): boolean {
        const pythonPatterns = [
            /^import\s+\w+/m,
            /^from\s+\w+\s+import/m,
            /^def\s+\w+\s*\(/m,
            /^class\s+\w+/m,
            /^\s*if\s+__name__\s*==\s*['"']__main__['"']/m
        ];

        return pythonPatterns.some(pattern => pattern.test(content));
    }

    /**
     * Checks if content has import statements
     */
    private static hasImportStatements(content: string): boolean {
        return /^(import|from)\s+\w+/m.test(content);
    }

    /**
     * Checks if script has valid ending
     */
    private static hasValidScriptEnding(content: string): boolean {
        const trimmed = content.trim();
        const validEndings = ['.py', 'pass', '}', ')', ']', '"""', "'''"];
        
        return validEndings.some(ending => trimmed.endsWith(ending));
    }

    /**
     * Checks if buffer contains binary content
     */
    private static hasBinaryContent(buffer: Buffer): boolean {
        // Check for high percentage of non-printable characters
        let nonPrintable = 0;
        const sampleSize = Math.min(1000, buffer.length);
        
        for (let i = 0; i < sampleSize; i++) {
            const byte = buffer[i];
            if (byte < 32 && byte !== 9 && byte !== 10 && byte !== 13) {
                nonPrintable++;
            }
        }
        
        return (nonPrintable / sampleSize) > 0.1; // More than 10% non-printable
    }
}
