/**
 * Broken CC Detection Service
 * 
 * Detects broken custom content, missing textures, corrupted files, and texture clashes.
 * Provides automatic detection with safe deletion capabilities.
 * 
 * Addresses Reddit request: "broken CC/texture clash detection with auto-deletion"
 */

import { Package } from '@s4tk/models';
import { DdsImageResource } from '@s4tk/models';
import { BinaryResourceType } from '@s4tk/models/enums';
import type { ResourceEntry } from '@s4tk/models/types';
import { PackageParserService } from '../../shared/PackageParserService';
import { FileValidationService } from '../../shared/FileValidationService';
import { RESOURCE_GROUPS } from '../../../constants/ResourceTypeRegistry';
import { UnifiedErrorHandler, UnifiedErrorCategory, UnifiedErrorSeverity, type UnifiedErrorInfo } from '../../shared/UnifiedErrorHandler';

export interface BrokenCCIssue {
    id: string;
    type: BrokenCCType;
    severity: BrokenCCSeverity;
    description: string;
    affectedFiles: string[];
    affectedResources: string[];
    autoFixAvailable: boolean;
    autoDeleteRecommended: boolean;
    repairSuggestions: string[];
    detectionMethod: string;
    confidence: number;
}

export interface BrokenCCAnalysisResult {
    fileName: string;
    filePath: string;
    isHealthy: boolean;
    issues: BrokenCCIssue[];
    overallSeverity: BrokenCCSeverity;
    autoDeleteRecommended: boolean;
    repairPossible: boolean;
    analysisTime: number;
    detectionMethods: string[];
}

export interface BrokenCCBatchResult {
    totalFiles: number;
    healthyFiles: number;
    brokenFiles: number;
    autoDeleteCandidates: number;
    repairableCandidates: number;
    results: BrokenCCAnalysisResult[];
    processingTime: number;
    detectionStats: Map<BrokenCCType, number>;
}

export enum BrokenCCType {
    CORRUPTED_PACKAGE = 'corrupted_package',
    MISSING_TEXTURES = 'missing_textures',
    BROKEN_MESH = 'broken_mesh',
    INVALID_RESOURCES = 'invalid_resources',
    TEXTURE_CLASH = 'texture_clash',
    MISSING_DEPENDENCIES = 'missing_dependencies',
    OUTDATED_FORMAT = 'outdated_format',
    INCOMPLETE_DOWNLOAD = 'incomplete_download',
    MALFORMED_XML = 'malformed_xml',
    BROKEN_REFERENCES = 'broken_references'
}

export enum BrokenCCSeverity {
    LOW = 'low',
    MEDIUM = 'medium',
    HIGH = 'high',
    CRITICAL = 'critical'
}

export interface BrokenCCDetectionOptions {
    enableDeepScan?: boolean;
    checkTextureIntegrity?: boolean;
    validateMeshReferences?: boolean;
    detectTextureClashes?: boolean;
    checkDependencies?: boolean;
    performanceMode?: boolean;
    autoDeleteThreshold?: BrokenCCSeverity;
}

/**
 * Service for detecting broken custom content and corrupted mod files
 */
export class BrokenCCDetectionService {

    /**
     * Maps BrokenCCType to UnifiedErrorCategory
     */
    private static mapBrokenCCTypeToUnified(brokenType: BrokenCCType): UnifiedErrorCategory {
        const mapping = {
            [BrokenCCType.CORRUPTED_PACKAGE]: UnifiedErrorCategory.CORRUPTED_PACKAGE,
            [BrokenCCType.MISSING_TEXTURES]: UnifiedErrorCategory.IMAGE_PROCESSING,
            [BrokenCCType.BROKEN_MESH]: UnifiedErrorCategory.RESOURCE_PARSING,
            [BrokenCCType.INVALID_RESOURCES]: UnifiedErrorCategory.INVALID_RESOURCES,
            [BrokenCCType.TEXTURE_CLASH]: UnifiedErrorCategory.IMAGE_PROCESSING,
            [BrokenCCType.OUTDATED_FORMAT]: UnifiedErrorCategory.PACKAGE_STRUCTURE,
            [BrokenCCType.INCOMPLETE_DOWNLOAD]: UnifiedErrorCategory.FILE_VALIDATION,
            [BrokenCCType.MALFORMED_XML]: UnifiedErrorCategory.XML_PARSING,
            [BrokenCCType.BROKEN_REFERENCES]: UnifiedErrorCategory.DEPENDENCY_RESOLUTION
        };
        return mapping[brokenType] || UnifiedErrorCategory.UNKNOWN;
    }

    /**
     * Maps BrokenCCSeverity to UnifiedErrorSeverity
     */
    private static mapBrokenCCSeverityToUnified(brokenSeverity: BrokenCCSeverity): UnifiedErrorSeverity {
        const mapping = {
            [BrokenCCSeverity.LOW]: UnifiedErrorSeverity.LOW,
            [BrokenCCSeverity.MEDIUM]: UnifiedErrorSeverity.MEDIUM,
            [BrokenCCSeverity.HIGH]: UnifiedErrorSeverity.HIGH,
            [BrokenCCSeverity.CRITICAL]: UnifiedErrorSeverity.CRITICAL
        };
        return mapping[brokenSeverity] || UnifiedErrorSeverity.MEDIUM;
    }

    /**
     * Creates a UnifiedErrorInfo from BrokenCCIssue data
     */
    private static createUnifiedErrorFromIssue(issue: BrokenCCIssue, context: string, filePath: string): UnifiedErrorInfo {
        return UnifiedErrorHandler.createError(
            new Error(issue.description),
            context,
            filePath,
            this.mapBrokenCCTypeToUnified(issue.type)
        );
    }
    private static readonly KNOWN_BROKEN_PATTERNS = [
        // Common corruption signatures
        { pattern: /^.{0,10}$/, type: BrokenCCType.INCOMPLETE_DOWNLOAD, description: 'File too small' },
        { pattern: /DBPF.{0,50}$/, type: BrokenCCType.INCOMPLETE_DOWNLOAD, description: 'Truncated DBPF header' },
        
        // Invalid resource patterns
        { pattern: /\x00{100,}/, type: BrokenCCType.CORRUPTED_PACKAGE, description: 'Large null byte sequences' },
        
        // Malformed XML patterns
        { pattern: /<[^>]*[^>]$/, type: BrokenCCType.MALFORMED_XML, description: 'Unclosed XML tags' },
        { pattern: /&[^;]*$/, type: BrokenCCType.MALFORMED_XML, description: 'Incomplete XML entities' }
    ];

    private static readonly TEXTURE_CLASH_INDICATORS = [
        'missing_texture',
        'texture_not_found',
        'invalid_dds',
        'corrupted_image',
        'broken_reference'
    ];

    // Removed CRITICAL_RESOURCE_TYPES - now using shared RESOURCE_GROUPS.CRITICAL_RESOURCES

    /**
     * Analyzes a single mod file for broken CC issues
     */
    public static async analyzeMod(
        buffer: Buffer,
        fileName: string,
        filePath: string,
        options: BrokenCCDetectionOptions = {}
    ): Promise<BrokenCCAnalysisResult> {
        const startTime = performance.now();
        const opts = {
            enableDeepScan: true,
            checkTextureIntegrity: true,
            validateMeshReferences: true,
            detectTextureClashes: true,
            checkDependencies: false,
            performanceMode: false,
            autoDeleteThreshold: BrokenCCSeverity.HIGH,
            ...options
        };

        const result: BrokenCCAnalysisResult = {
            fileName,
            filePath,
            isHealthy: true,
            issues: [],
            overallSeverity: BrokenCCSeverity.LOW,
            autoDeleteRecommended: false,
            repairPossible: false,
            analysisTime: 0,
            detectionMethods: []
        };

        try {
            // Use shared file validation service
            if (fileName.toLowerCase().endsWith('.package')) {
                const packageValidation = FileValidationService.validatePackageStructure(buffer, fileName);
                this.convertValidationToIssues(packageValidation, result);

                if (packageValidation.canParse) {
                    await this.validatePackageContent(buffer, fileName, result, opts);
                }
            } else if (fileName.toLowerCase().endsWith('.ts4script')) {
                const scriptValidation = FileValidationService.validateScriptFile(buffer, fileName);
                this.convertValidationToIssues(scriptValidation, result);
            } else {
                const basicValidation = FileValidationService.validateFileBasics(buffer, fileName);
                this.convertValidationToIssues(basicValidation, result);
            }

            // Determine overall health
            this.assessOverallHealth(result, opts);

        } catch (error) {
            // Create unified error for logging/tracking
            const unifiedError = UnifiedErrorHandler.createError(
                error,
                'BrokenCCDetectionService.analyzeMod',
                filePath,
                UnifiedErrorCategory.CORRUPTED_PACKAGE
            );

            // Create legacy issue for backward compatibility
            result.issues.push({
                id: `critical-${Date.now()}`,
                type: BrokenCCType.CORRUPTED_PACKAGE,
                severity: BrokenCCSeverity.CRITICAL,
                description: `Analysis failed: ${error.message}`,
                affectedFiles: [fileName],
                affectedResources: [],
                autoFixAvailable: false,
                autoDeleteRecommended: true,
                repairSuggestions: ['Re-download the mod file'],
                detectionMethod: 'exception_analysis',
                confidence: 95
            });
            result.isHealthy = false;
            result.overallSeverity = BrokenCCSeverity.CRITICAL;
            result.autoDeleteRecommended = true;
        }

        result.analysisTime = performance.now() - startTime;
        return result;
    }

    /**
     * Converts shared validation results to broken CC issues
     */
    private static convertValidationToIssues(
        validation: any,
        result: BrokenCCAnalysisResult
    ): void {
        result.detectionMethods.push('shared_validation');

        // Convert validation errors to issues
        for (const error of validation.errors || []) {
            const severity = this.mapValidationSeverity(error.severity);
            const type = this.mapValidationErrorType(error.code);

            result.issues.push({
                id: `validation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                type,
                severity,
                description: error.message,
                affectedFiles: [result.fileName],
                affectedResources: [],
                autoFixAvailable: false,
                autoDeleteRecommended: severity === BrokenCCSeverity.CRITICAL,
                repairSuggestions: error.suggestion ? [error.suggestion] : [],
                detectionMethod: 'shared_validation',
                confidence: 90
            });
        }

        // Convert validation warnings to lower severity issues
        for (const warning of validation.warnings || []) {
            result.issues.push({
                id: `warning-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                type: BrokenCCType.OUTDATED_FORMAT,
                severity: BrokenCCSeverity.LOW,
                description: warning.message,
                affectedFiles: [result.fileName],
                affectedResources: [],
                autoFixAvailable: false,
                autoDeleteRecommended: false,
                repairSuggestions: [warning.impact],
                detectionMethod: 'shared_validation',
                confidence: 70
            });
        }
    }

    /**
     * Maps validation severity to broken CC severity
     */
    private static mapValidationSeverity(severity: string): BrokenCCSeverity {
        switch (severity) {
            case 'critical': return BrokenCCSeverity.CRITICAL;
            case 'high': return BrokenCCSeverity.HIGH;
            case 'medium': return BrokenCCSeverity.MEDIUM;
            case 'low': return BrokenCCSeverity.LOW;
            default: return BrokenCCSeverity.MEDIUM;
        }
    }

    /**
     * Maps validation error codes to broken CC types
     */
    private static mapValidationErrorType(code: string): BrokenCCType {
        switch (code) {
            case 'EMPTY_FILE':
            case 'FILE_TOO_SMALL':
            case 'PACKAGE_TOO_SMALL':
                return BrokenCCType.INCOMPLETE_DOWNLOAD;
            case 'INVALID_SIGNATURE':
            case 'PACKAGE_STRUCTURE_ERROR':
            case 'PACKAGE_PARSE_ERROR':
                return BrokenCCType.CORRUPTED_PACKAGE;
            case 'PYTHON_SYNTAX_ERROR':
            case 'BINARY_IN_SCRIPT':
                return BrokenCCType.MALFORMED_XML;
            case 'NULL_BYTE_CORRUPTION':
                return BrokenCCType.CORRUPTED_PACKAGE;
            default:
                return BrokenCCType.INVALID_RESOURCES;
        }
    }

    /**
     * Validates package content using shared parser
     */
    private static async validatePackageContent(
        buffer: Buffer,
        fileName: string,
        result: BrokenCCAnalysisResult,
        options: BrokenCCDetectionOptions
    ): Promise<void> {
        result.detectionMethods.push('package_content_validation');

        try {
            // Parse package using shared service
            const parseResult = PackageParserService.parsePackage(buffer, fileName, {
                decompressBuffers: false,
                loadRaw: true,
                enableCaching: true
            });

            const s4tkPackage = parseResult.package;

            // Validate critical resources using shared resource groups
            await this.validateCriticalResources(s4tkPackage, result);

            // Check for texture integrity if enabled
            if (options.checkTextureIntegrity) {
                await this.validateTextureIntegrity(s4tkPackage, result);
            }

            // Check for mesh references if enabled
            if (options.validateMeshReferences) {
                await this.validateMeshReferences(s4tkPackage, result);
            }

            // Detect texture clashes if enabled
            if (options.detectTextureClashes) {
                await this.detectTextureClashes(s4tkPackage, result);
            }

        } catch (error) {
            const severity = this.classifyPackageError(error.message);
            result.issues.push({
                id: `package-error-${Date.now()}`,
                type: BrokenCCType.CORRUPTED_PACKAGE,
                severity,
                description: `Package content validation failed: ${error.message}`,
                affectedFiles: [result.fileName],
                affectedResources: [],
                autoFixAvailable: false,
                autoDeleteRecommended: severity === BrokenCCSeverity.CRITICAL,
                repairSuggestions: this.getRepairSuggestions(error.message),
                detectionMethod: 'package_content_validation',
                confidence: 90
            });
        }
    }

    /**
     * Validates critical resources within the package
     */
    private static async validateCriticalResources(
        s4tkPackage: Package,
        result: BrokenCCAnalysisResult
    ): Promise<void> {
        // Use shared resource groups for critical resources
        const criticalResources = PackageParserService.getResourcesByType(
            s4tkPackage,
            RESOURCE_GROUPS.CRITICAL_RESOURCES
        );

        for (const resource of criticalResources) {
            try {
                // Try to access resource data
                if (!resource.value || (resource.value as Buffer).length === 0) {
                    result.issues.push({
                        id: `empty-resource-${Date.now()}`,
                        type: BrokenCCType.INVALID_RESOURCES,
                        severity: BrokenCCSeverity.HIGH,
                        description: `Critical resource is empty: ${resource.key.type.toString(16)}`,
                        affectedFiles: [result.fileName],
                        affectedResources: [`${resource.key.type}-${resource.key.group}-${resource.key.instance}`],
                        autoFixAvailable: false,
                        autoDeleteRecommended: false,
                        repairSuggestions: ['Check with mod creator for updated version'],
                        detectionMethod: 'resource_validation',
                        confidence: 95
                    });
                }
            } catch (error) {
                result.issues.push({
                    id: `resource-error-${Date.now()}`,
                    type: BrokenCCType.INVALID_RESOURCES,
                    severity: BrokenCCSeverity.MEDIUM,
                    description: `Resource validation failed: ${error.message}`,
                    affectedFiles: [result.fileName],
                    affectedResources: [`${resource.key.type}-${resource.key.group}-${resource.key.instance}`],
                    autoFixAvailable: false,
                    autoDeleteRecommended: false,
                    repairSuggestions: ['Resource may be corrupted but mod might still work'],
                    detectionMethod: 'resource_validation',
                    confidence: 75
                });
            }
        }
    }

    /**
     * Validates texture integrity
     */
    private static async validateTextureIntegrity(
        s4tkPackage: Package,
        result: BrokenCCAnalysisResult
    ): Promise<void> {
        // Use shared resource groups for texture resources
        const textureResources = PackageParserService.getResourcesByType(
            s4tkPackage,
            RESOURCE_GROUPS.TEXTURE_RESOURCES
        );

        for (const texture of textureResources) {
            try {
                if (texture.key.type === BinaryResourceType.DdsImage) {
                    // Try to parse DDS image
                    const ddsResource = DdsImageResource.from(texture.value as Buffer);
                    const imageInfo = ddsResource.image;
                    
                    if (!imageInfo || !imageInfo.width || !imageInfo.height) {
                        result.issues.push({
                            id: `invalid-texture-${Date.now()}`,
                            type: BrokenCCType.MISSING_TEXTURES,
                            severity: BrokenCCSeverity.MEDIUM,
                            description: 'DDS texture has invalid dimensions',
                            affectedFiles: [result.fileName],
                            affectedResources: [`${texture.key.type}-${texture.key.group}-${texture.key.instance}`],
                            autoFixAvailable: false,
                            autoDeleteRecommended: false,
                            repairSuggestions: ['Texture may cause visual issues'],
                            detectionMethod: 'texture_validation',
                            confidence: 80
                        });
                    }
                }
            } catch (error) {
                result.issues.push({
                    id: `texture-error-${Date.now()}`,
                    type: BrokenCCType.MISSING_TEXTURES,
                    severity: BrokenCCSeverity.MEDIUM,
                    description: `Texture validation failed: ${error.message}`,
                    affectedFiles: [result.fileName],
                    affectedResources: [`${texture.key.type}-${texture.key.group}-${texture.key.instance}`],
                    autoFixAvailable: false,
                    autoDeleteRecommended: false,
                    repairSuggestions: ['Texture may be corrupted but mod might still work'],
                    detectionMethod: 'texture_validation',
                    confidence: 70
                });
            }
        }
    }

    /**
     * Validates mesh references
     */
    private static async validateMeshReferences(
        s4tkPackage: Package,
        result: BrokenCCAnalysisResult
    ): Promise<void> {
        // Use shared resource groups for mesh resources
        const meshResources = PackageParserService.getResourcesByType(
            s4tkPackage,
            RESOURCE_GROUPS.MESH_RESOURCES
        );

        // Basic mesh validation - check if mesh resources exist and are not empty
        for (const mesh of meshResources) {
            if (!mesh.value || (mesh.value as Buffer).length < 100) {
                result.issues.push({
                    id: `broken-mesh-${Date.now()}`,
                    type: BrokenCCType.BROKEN_MESH,
                    severity: BrokenCCSeverity.HIGH,
                    description: 'Mesh resource is too small or empty',
                    affectedFiles: [result.fileName],
                    affectedResources: [`${mesh.key.type}-${mesh.key.group}-${mesh.key.instance}`],
                    autoFixAvailable: false,
                    autoDeleteRecommended: false,
                    repairSuggestions: ['Mesh may be corrupted - check for updated version'],
                    detectionMethod: 'mesh_validation',
                    confidence: 85
                });
            }
        }
    }

    /**
     * Detects texture clashes
     */
    private static async detectTextureClashes(
        s4tkPackage: Package,
        result: BrokenCCAnalysisResult
    ): Promise<void> {
        // Use shared resource groups for texture resources
        const textureResources = PackageParserService.getResourcesByType(
            s4tkPackage,
            [BinaryResourceType.DdsImage, BinaryResourceType.DstImage]
        );

        const textureMap = new Map<string, ResourceEntry[]>();

        for (const entry of textureResources) {
            const key = `${entry.key.group}-${entry.key.instance}`;
            if (!textureMap.has(key)) {
                textureMap.set(key, []);
            }
            textureMap.get(key)!.push(entry);
        }

        // Check for duplicate texture references
        for (const [key, textures] of textureMap) {
            if (textures.length > 1) {
                result.issues.push({
                    id: `texture-clash-${Date.now()}`,
                    type: BrokenCCType.TEXTURE_CLASH,
                    severity: BrokenCCSeverity.MEDIUM,
                    description: `Multiple textures with same reference: ${key}`,
                    affectedFiles: [result.fileName],
                    affectedResources: textures.map(t => `${t.key.type}-${t.key.group}-${t.key.instance}`),
                    autoFixAvailable: false,
                    autoDeleteRecommended: false,
                    repairSuggestions: ['May cause texture conflicts in-game'],
                    detectionMethod: 'texture_clash_detection',
                    confidence: 70
                });
            }
        }
    }

    /**
     * Validates script files
     */
    private static async validateScriptFile(
        buffer: Buffer,
        result: BrokenCCAnalysisResult
    ): Promise<void> {
        result.detectionMethods.push('script_validation');

        try {
            // Basic Python syntax validation
            const content = buffer.toString('utf8');
            
            // Check for common Python syntax errors
            if (content.includes('SyntaxError') || content.includes('IndentationError')) {
                result.issues.push({
                    id: `script-syntax-${Date.now()}`,
                    type: BrokenCCType.MALFORMED_XML,
                    severity: BrokenCCSeverity.HIGH,
                    description: 'Script contains syntax errors',
                    affectedFiles: [result.fileName],
                    affectedResources: [],
                    autoFixAvailable: false,
                    autoDeleteRecommended: true,
                    repairSuggestions: ['Contact mod creator for fixed version'],
                    detectionMethod: 'script_validation',
                    confidence: 95
                });
            }

            // Check for incomplete files
            if (!content.trim().endsWith(('.py', 'pass', '}', ')', ']'))) {
                result.issues.push({
                    id: `incomplete-script-${Date.now()}`,
                    type: BrokenCCType.INCOMPLETE_DOWNLOAD,
                    severity: BrokenCCSeverity.MEDIUM,
                    description: 'Script file appears incomplete',
                    affectedFiles: [result.fileName],
                    affectedResources: [],
                    autoFixAvailable: false,
                    autoDeleteRecommended: false,
                    repairSuggestions: ['Re-download the script file'],
                    detectionMethod: 'script_validation',
                    confidence: 60
                });
            }

        } catch (error) {
            result.issues.push({
                id: `script-error-${Date.now()}`,
                type: BrokenCCType.CORRUPTED_PACKAGE,
                severity: BrokenCCSeverity.MEDIUM,
                description: `Script validation failed: ${error.message}`,
                affectedFiles: [result.fileName],
                affectedResources: [],
                autoFixAvailable: false,
                autoDeleteRecommended: false,
                repairSuggestions: ['Script may have encoding issues'],
                detectionMethod: 'script_validation',
                confidence: 70
            });
        }
    }

    /**
     * Assesses overall health and determines recommendations
     */
    private static assessOverallHealth(
        result: BrokenCCAnalysisResult,
        options: BrokenCCDetectionOptions
    ): void {
        if (result.issues.length === 0) {
            result.isHealthy = true;
            result.overallSeverity = BrokenCCSeverity.LOW;
            return;
        }

        // Determine overall severity
        const severities = result.issues.map(issue => issue.severity);
        if (severities.includes(BrokenCCSeverity.CRITICAL)) {
            result.overallSeverity = BrokenCCSeverity.CRITICAL;
        } else if (severities.includes(BrokenCCSeverity.HIGH)) {
            result.overallSeverity = BrokenCCSeverity.HIGH;
        } else if (severities.includes(BrokenCCSeverity.MEDIUM)) {
            result.overallSeverity = BrokenCCSeverity.MEDIUM;
        } else {
            result.overallSeverity = BrokenCCSeverity.LOW;
        }

        // Determine if auto-delete is recommended
        const autoDeleteThreshold = options.autoDeleteThreshold || BrokenCCSeverity.HIGH;
        result.autoDeleteRecommended = this.shouldAutoDelete(result.overallSeverity, autoDeleteThreshold) ||
            result.issues.some(issue => issue.autoDeleteRecommended);

        // Determine if repair is possible
        result.repairPossible = result.issues.some(issue => issue.autoFixAvailable);

        result.isHealthy = result.overallSeverity === BrokenCCSeverity.LOW && result.issues.length <= 1;
    }

    /**
     * Classifies package errors by severity
     */
    private static classifyPackageError(errorMessage: string): BrokenCCSeverity {
        const message = errorMessage.toLowerCase();
        
        if (message.includes('buffer') || message.includes('truncated') || message.includes('corrupt')) {
            return BrokenCCSeverity.CRITICAL;
        }
        if (message.includes('format') || message.includes('invalid') || message.includes('malformed')) {
            return BrokenCCSeverity.HIGH;
        }
        if (message.includes('compression') || message.includes('decompression')) {
            return BrokenCCSeverity.MEDIUM;
        }
        
        return BrokenCCSeverity.MEDIUM;
    }

    /**
     * Gets repair suggestions based on error message
     */
    private static getRepairSuggestions(errorMessage: string): string[] {
        const message = errorMessage.toLowerCase();
        
        if (message.includes('buffer') || message.includes('truncated')) {
            return ['Re-download the mod file', 'Check download integrity'];
        }
        if (message.includes('compression')) {
            return ['Try extracting with Sims 4 Studio first', 'Check for updated version'];
        }
        if (message.includes('format')) {
            return ['Ensure file is a valid .package file', 'Contact mod creator'];
        }
        
        return ['Re-download the mod file', 'Check for updated version'];
    }

    /**
     * Determines if auto-delete should be recommended
     */
    private static shouldAutoDelete(severity: BrokenCCSeverity, threshold: BrokenCCSeverity): boolean {
        const severityOrder = [
            BrokenCCSeverity.LOW,
            BrokenCCSeverity.MEDIUM,
            BrokenCCSeverity.HIGH,
            BrokenCCSeverity.CRITICAL
        ];
        
        const severityIndex = severityOrder.indexOf(severity);
        const thresholdIndex = severityOrder.indexOf(threshold);
        
        return severityIndex >= thresholdIndex;
    }

    /**
     * Analyzes multiple mods in batch
     */
    public static async analyzeBatch(
        modFiles: Array<{ buffer: Buffer; fileName: string; filePath: string }>,
        options: BrokenCCDetectionOptions = {}
    ): Promise<BrokenCCBatchResult> {
        const startTime = performance.now();
        const results: BrokenCCAnalysisResult[] = [];
        const detectionStats = new Map<BrokenCCType, number>();

        for (const mod of modFiles) {
            const result = await this.analyzeMod(mod.buffer, mod.fileName, mod.filePath, options);
            results.push(result);

            // Update detection stats
            for (const issue of result.issues) {
                detectionStats.set(issue.type, (detectionStats.get(issue.type) || 0) + 1);
            }
        }

        const healthyFiles = results.filter(r => r.isHealthy).length;
        const brokenFiles = results.filter(r => !r.isHealthy).length;
        const autoDeleteCandidates = results.filter(r => r.autoDeleteRecommended).length;
        const repairableCandidates = results.filter(r => r.repairPossible).length;

        return {
            totalFiles: modFiles.length,
            healthyFiles,
            brokenFiles,
            autoDeleteCandidates,
            repairableCandidates,
            results,
            processingTime: performance.now() - startTime,
            detectionStats
        };
    }

    /**
     * Gets a summary of common broken CC patterns for educational purposes
     */
    public static getBrokenCCPatterns(): Array<{ type: BrokenCCType; description: string; indicators: string[] }> {
        return [
            {
                type: BrokenCCType.CORRUPTED_PACKAGE,
                description: 'Package file is corrupted or incomplete',
                indicators: ['File too small', 'Invalid DBPF header', 'Parsing errors']
            },
            {
                type: BrokenCCType.MISSING_TEXTURES,
                description: 'Textures are missing or corrupted',
                indicators: ['Invalid DDS files', 'Missing texture references', 'Broken image data']
            },
            {
                type: BrokenCCType.BROKEN_MESH,
                description: 'Mesh data is corrupted or incomplete',
                indicators: ['Empty mesh resources', 'Invalid model data', 'Missing LOD levels']
            },
            {
                type: BrokenCCType.TEXTURE_CLASH,
                description: 'Multiple textures conflict with each other',
                indicators: ['Duplicate texture IDs', 'Conflicting references', 'Overlapping resources']
            },
            {
                type: BrokenCCType.INCOMPLETE_DOWNLOAD,
                description: 'File was not downloaded completely',
                indicators: ['Truncated file', 'Unexpected end of file', 'Missing data']
            }
        ];
    }
}
