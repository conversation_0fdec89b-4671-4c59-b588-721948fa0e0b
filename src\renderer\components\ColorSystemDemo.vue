<template>
  <div class="color-demo">
    <div class="demo-header">
      <h1 class="gradient-text-coral-teal">Simonitor Color System Demo</h1>
      <p class="text-secondary">Comprehensive showcase of the modern OKLCH-based color system</p>
    </div>

    <!-- Theme Controls -->
    <div class="demo-section">
      <h2>Theme Controls</h2>
      <div class="theme-controls">
        <button @click="toggleColorScheme" class="btn btn-primary">
          Toggle {{ isDark ? 'Light' : 'Dark' }} Mode
        </button>
        <button @click="setVariant('high-contrast')" class="btn btn-outline">
          High Contrast
        </button>
        <button @click="setVariant('default')" class="btn btn-outline">
          Default
        </button>
      </div>
    </div>

    <!-- Color Palette -->
    <div class="demo-section">
      <h2>Core Color Palette</h2>
      <div class="color-grid">
        <div class="color-family">
          <h3><PERSON><PERSON> (Primary)</h3>
          <p class="color-description">Energy, growth, main actions</p>
          <div class="color-swatches">
            <div v-for="shade in limeGreenShades" :key="shade"
                 :style="`background-color: var(--lime-green-${shade})`"
                 class="color-swatch"
                 :title="`lime-green-${shade}`">
              {{ shade }}
            </div>
          </div>
        </div>

        <div class="color-family">
          <h3>Golden Yellow (Secondary)</h3>
          <p class="color-description">Warmth, optimism, secondary actions</p>
          <div class="color-swatches">
            <div v-for="shade in goldenYellowShades" :key="shade"
                 :style="`background-color: var(--golden-yellow-${shade})`"
                 class="color-swatch"
                 :title="`golden-yellow-${shade}`">
              {{ shade }}
            </div>
          </div>
        </div>

        <div class="color-family">
          <h3>Vibrant Orange (Warning)</h3>
          <p class="color-description">Energy, attention, warnings</p>
          <div class="color-swatches">
            <div v-for="shade in vibrantOrangeShades" :key="shade"
                 :style="`background-color: var(--vibrant-orange-${shade})`"
                 class="color-swatch"
                 :title="`vibrant-orange-${shade}`">
              {{ shade }}
            </div>
          </div>
        </div>

        <div class="color-family">
          <h3>Deep Orange (Premium)</h3>
          <p class="color-description">Authority, premium features, script files</p>
          <div class="color-swatches">
            <div v-for="shade in deepOrangeShades" :key="shade"
                 :style="`background-color: var(--deep-orange-${shade})`"
                 class="color-swatch"
                 :title="`deep-orange-${shade}`">
              {{ shade }}
            </div>
          </div>
        </div>

        <div class="color-family">
          <h3>Crimson Red (Error)</h3>
          <p class="color-description">Critical issues, errors, urgent attention</p>
          <div class="color-swatches">
            <div v-for="shade in crimsonRedShades" :key="shade"
                 :style="`background-color: var(--crimson-red-${shade})`"
                 class="color-swatch"
                 :title="`crimson-red-${shade}`">
              {{ shade }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Semantic Colors -->
    <div class="demo-section">
      <h2>Semantic Color Usage</h2>
      <div class="semantic-demo">
        <button class="btn btn-primary">Primary Action</button>
        <button class="btn btn-secondary">Secondary Action</button>
        <button class="btn btn-success">Success Action</button>
        <button class="btn btn-premium">Premium Feature</button>
        <button class="btn btn-outline">Outline Button</button>
      </div>
    </div>

    <!-- Badges and Status -->
    <div class="demo-section">
      <h2>Badges and Status Indicators</h2>
      <div class="badge-demo">
        <span class="badge badge-primary">Primary</span>
        <span class="badge badge-secondary">Secondary</span>
        <span class="badge badge-success">Success</span>
        <span class="badge badge-warning">Warning</span>
        <span class="badge badge-error">Error</span>
        <span class="badge badge-premium">Premium</span>
      </div>
    </div>

    <!-- Quality Scores -->
    <div class="demo-section">
      <h2>Quality Score Colors</h2>
      <div class="quality-demo">
        <div class="quality-item bg-quality-excellent">
          <span class="quality-score">95%</span>
          <span class="quality-label">Excellent</span>
        </div>
        <div class="quality-item bg-quality-good">
          <span class="quality-score">82%</span>
          <span class="quality-label">Good</span>
        </div>
        <div class="quality-item bg-quality-fair">
          <span class="quality-score">67%</span>
          <span class="quality-label">Fair</span>
        </div>
        <div class="quality-item bg-quality-poor">
          <span class="quality-score">45%</span>
          <span class="quality-label">Poor</span>
        </div>
      </div>
    </div>

    <!-- File Types -->
    <div class="demo-section">
      <h2>File Type Indicators</h2>
      <div class="file-type-demo">
        <span class="badge file-type--package">PACKAGE</span>
        <span class="badge file-type--ts4script">TS4SCRIPT</span>
        <span class="badge file-type--resource">RESOURCE</span>
        <span class="badge file-type--unknown">UNKNOWN</span>
      </div>
    </div>

    <!-- Interactive Elements -->
    <div class="demo-section">
      <h2>Interactive Elements</h2>
      <div class="interactive-demo">
        <div class="card hover-lift">
          <h3>Hover Lift Card</h3>
          <p>This card lifts on hover with smooth animation</p>
        </div>
        
        <div class="card hover-glow">
          <h3>Hover Glow Card</h3>
          <p>This card glows on hover with coral accent</p>
        </div>
      </div>
    </div>

    <!-- Form Elements -->
    <div class="demo-section">
      <h2>Form Elements</h2>
      <div class="form-demo">
        <input type="text" class="input" placeholder="Search mods..." />
        <select class="select">
          <option>All Types</option>
          <option>Package Files</option>
          <option>Script Files</option>
        </select>
        <button class="btn btn-primary">Submit</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useTheme } from '../composables/useTheme';

const { isDark, toggleColorScheme, setVariant } = useTheme();

const limeGreenShades = ref([50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950]);
const goldenYellowShades = ref([50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950]);
const vibrantOrangeShades = ref([50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950]);
const deepOrangeShades = ref([50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950]);
const crimsonRedShades = ref([50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950]);
</script>

<style scoped>
.color-demo {
  padding: var(--space-8);
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.demo-header h1 {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-2);
}

.demo-section {
  margin-bottom: var(--space-8);
}

.demo-section h2 {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.theme-controls {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
}

.color-family h3 {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  margin-bottom: var(--space-1);
  color: var(--text-primary);
}

.color-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-3);
  font-style: italic;
}

.color-swatches {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-1);
}

.color-swatch {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  cursor: pointer;
  transition: transform var(--duration-200) var(--ease-out);
}

.color-swatch:hover {
  transform: scale(1.1);
}

.semantic-demo,
.badge-demo,
.file-type-demo {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.quality-demo {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-4);
}

.quality-item {
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  text-align: center;
  border: 1px solid var(--border-light);
}

.quality-score {
  display: block;
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-1);
}

.quality-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.interactive-demo {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
}

.form-demo {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
  align-items: center;
}

.form-demo .input,
.form-demo .select {
  min-width: 200px;
}
</style>
