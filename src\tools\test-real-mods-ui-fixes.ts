/**
 * Test UI Fixes with Real Mods from Assets Folder
 * 
 * This test validates that:
 * 1. StringTable analysis errors are fixed
 * 2. Data mapping works correctly
 * 3. Mod descriptions use actual extracted data
 * 4. UI shows meaningful information instead of generic descriptions
 */

import { PackageAnalysisService } from '../services/analysis/PackageAnalysisService.js';
import path from 'path';
import fs from 'fs';

// Simulate the getModDescription function from ModCard.vue
function getModDescription(modData: any): string {
  // Use actual description from StringTable if available
  if (modData?.actualDescription) {
    return modData.actualDescription;
  }

  // Use extracted mod name and author to create a meaningful description
  if (modData?.actualModName || modData?.author) {
    const modName = modData.actualModName || 'Custom Content';
    const author = modData.author;
    
    if (author && modName) {
      return `${modName} by ${author} - Custom content for The Sims 4.`;
    } else if (modName) {
      return `${modName} - Custom content for The Sims 4.`;
    } else if (author) {
      return `Custom content by ${author} for The Sims 4.`;
    }
  }

  // Fallback
  return 'Custom content for The Sims 4.';
}

// Simulate App.vue data mapping
function mapAnalysisResultToUI(result: any) {
  return {
    fileName: result.filePath ? result.filePath.split(/[/\\]/).pop() : result.fileName || 'Unknown',
    actualModName: result.intelligence?.stringTableData?.modName || result.metadata?.modName || result.modName || null,
    actualDescription: result.intelligence?.stringTableData?.description || result.metadata?.description || null,
    author: result.metadata?.author || result.author || null,
    hasStringTable: !!(result.intelligence?.stringTableData?.customStringCount > 0),
    fileExtension: result.fileExtension || (result.filePath ? path.extname(result.filePath) : '.package'),
    fileSize: result.fileSize || 0
  };
}

async function testRealModsUIFixes() {
  console.log('🧪 Testing UI Fixes with Real Mods from Assets Folder');
  console.log('=' .repeat(80));
  console.log();

  const assetsPath = path.join(process.cwd(), 'assets');
  const analysisService = new PackageAnalysisService();
  
  // Get a mix of package and script files
  const allFiles = fs.readdirSync(assetsPath);
  const packageFiles = allFiles.filter(f => f.endsWith('.package')).slice(0, 3);
  const scriptFiles = allFiles.filter(f => f.endsWith('.ts4script')).slice(0, 3);
  const testFiles = [...packageFiles, ...scriptFiles];
  
  console.log(`📁 Testing with ${testFiles.length} real mod files:`);
  testFiles.forEach(f => console.log(`  - ${f}`));
  console.log();

  let successCount = 0;
  let errorCount = 0;
  let improvedDescriptions = 0;

  for (const fileName of testFiles) {
    const filePath = path.join(assetsPath, fileName);
    
    try {
      console.log(`\n📦 Testing: ${fileName}`);
      console.log('-'.repeat(60));
      
      const buffer = fs.readFileSync(filePath);
      const result = await analysisService.detailedAnalyzeAsync(buffer, filePath);
      
      // Map data like App.vue does
      const uiData = mapAnalysisResultToUI(result);
      
      // Generate description like ModCard.vue does
      const description = getModDescription(uiData);
      
      console.log(`✅ Analysis successful`);
      console.log(`📝 File: ${uiData.fileName}`);
      console.log(`👤 Author: ${uiData.author || 'Not detected'}`);
      console.log(`🏷️  Mod Name: ${uiData.actualModName || 'Not detected'}`);
      console.log(`📄 Description: "${description}"`);
      console.log(`📊 StringTable: ${uiData.hasStringTable ? 'Found' : 'Not found'}`);
      
      // Check if description is improved (not generic)
      if (description !== 'Custom content for The Sims 4.' && 
          !description.includes('Adds new') && 
          !description.includes('for Build/Buy mode')) {
        improvedDescriptions++;
        console.log(`🎉 IMPROVED: Description uses actual mod data!`);
      }
      
      successCount++;
      
    } catch (error) {
      console.error(`❌ Error testing ${fileName}:`, error.message);
      errorCount++;
    }
  }
  
  console.log('\n' + '='.repeat(80));
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('='.repeat(80));
  console.log(`✅ Successful analyses: ${successCount}/${testFiles.length}`);
  console.log(`❌ Failed analyses: ${errorCount}/${testFiles.length}`);
  console.log(`🎉 Improved descriptions: ${improvedDescriptions}/${successCount}`);
  console.log(`📈 Success rate: ${Math.round((successCount / testFiles.length) * 100)}%`);
  console.log(`🚀 Description improvement rate: ${Math.round((improvedDescriptions / successCount) * 100)}%`);
  
  if (improvedDescriptions > 0) {
    console.log('\n✅ UI FIXES WORKING: Mod descriptions now use actual extracted data!');
  } else {
    console.log('\n⚠️  UI FIXES NEED MORE WORK: Still showing generic descriptions');
  }
}

testRealModsUIFixes().catch(console.error);
