<template>
  <div class="conflict-visualization">
    <!-- Header -->
    <div class="conflict-header">
      <div class="header-info">
        <h3 class="header-title">
          <ExclamationTriangleIcon class="w-5 h-5 text-warning" />
          Mod Conflicts
        </h3>
        <p class="header-subtitle">
          {{ totalConflicts }} conflicts detected • Risk Level: 
          <span :class="riskLevelClass">{{ overallRisk.toUpperCase() }}</span>
        </p>
      </div>
      
      <div class="header-actions">
        <!-- Filter Controls -->
        <select v-model="selectedSeverity" class="filter-select">
          <option value="">All Severities</option>
          <option value="critical">Critical</option>
          <option value="high">High</option>
          <option value="medium">Medium</option>
          <option value="low">Low</option>
        </select>
        
        <select v-model="selectedType" class="filter-select">
          <option value="">All Types</option>
          <option value="exact_duplicate">Exact Duplicates</option>
          <option value="override_conflict">Override Conflicts</option>
          <option value="script_module_conflict">Script Conflicts</option>
          <option value="cas_semantic_conflict">CAS Conflicts</option>
          <option value="organization">Organization Issues</option>
        </select>
        
        <!-- View Toggle -->
        <div class="view-toggle">
          <button
            @click="viewMode = 'grouped'"
            :class="{ active: viewMode === 'grouped' }"
            class="toggle-btn"
          >
            <Squares2X2Icon class="w-4 h-4" />
            Grouped
          </button>
          <button
            @click="viewMode = 'list'"
            :class="{ active: viewMode === 'list' }"
            class="toggle-btn"
          >
            <ListBulletIcon class="w-4 h-4" />
            List
          </button>
        </div>
      </div>
    </div>
    
    <!-- Summary Stats -->
    <div class="conflict-summary">
      <div class="summary-card critical">
        <div class="card-icon">
          <XCircleIcon class="w-6 h-6" />
        </div>
        <div class="card-content">
          <div class="card-number">{{ conflictsBySeverity.critical || 0 }}</div>
          <div class="card-label">Critical</div>
        </div>
      </div>
      
      <div class="summary-card high">
        <div class="card-icon">
          <ExclamationTriangleIcon class="w-6 h-6" />
        </div>
        <div class="card-content">
          <div class="card-number">{{ conflictsBySeverity.high || 0 }}</div>
          <div class="card-label">High</div>
        </div>
      </div>
      
      <div class="summary-card medium">
        <div class="card-icon">
          <ExclamationCircleIcon class="w-6 h-6" />
        </div>
        <div class="card-content">
          <div class="card-number">{{ conflictsBySeverity.medium || 0 }}</div>
          <div class="card-label">Medium</div>
        </div>
      </div>
      
      <div class="summary-card low">
        <div class="card-icon">
          <InformationCircleIcon class="w-6 h-6" />
        </div>
        <div class="card-content">
          <div class="card-number">{{ conflictsBySeverity.low || 0 }}</div>
          <div class="card-label">Low</div>
        </div>
      </div>
    </div>
    
    <!-- Loading State -->
    <div v-if="isLoading" class="conflict-loading">
      <div class="loading-spinner"></div>
      <p>Analyzing conflicts...</p>
    </div>
    
    <!-- Empty State -->
    <div v-else-if="filteredConflicts.length === 0" class="conflict-empty">
      <CheckCircleIcon class="empty-icon text-success" />
      <h3>{{ totalConflicts === 0 ? 'No conflicts detected!' : 'No conflicts match your filters' }}</h3>
      <p>{{ totalConflicts === 0 ? 'Your mod collection looks clean.' : 'Try adjusting your filters to see more results.' }}</p>
    </div>
    
    <!-- Conflict Content -->
    <div v-else class="conflict-content">
      <!-- Grouped View -->
      <div v-if="viewMode === 'grouped'" class="conflict-groups">
        <div
          v-for="(group, severity) in groupedConflicts"
          :key="severity"
          class="conflict-group"
          :class="`severity-${severity}`"
        >
          <div class="group-header">
            <div class="group-title">
              <component :is="getSeverityIcon(severity)" class="w-5 h-5" />
              {{ formatSeverity(severity) }} Priority
              <span class="group-count">({{ group.length }})</span>
            </div>
            <button
              @click="toggleGroup(severity)"
              class="group-toggle"
            >
              <ChevronDownIcon 
                class="w-4 h-4 transition-transform"
                :class="{ 'rotate-180': !collapsedGroups[severity] }"
              />
            </button>
          </div>
          
          <div v-if="!collapsedGroups[severity]" class="group-content">
            <ConflictCard
              v-for="conflict in group"
              :key="conflict.id"
              :conflict="conflict"
              @resolve="resolveConflict"
              @ignore="ignoreConflict"
              @show-details="showConflictDetails"
            />
          </div>
        </div>
      </div>
      
      <!-- List View -->
      <div v-else class="conflict-list">
        <ConflictCard
          v-for="conflict in filteredConflicts"
          :key="conflict.id"
          :conflict="conflict"
          @resolve="resolveConflict"
          @ignore="ignoreConflict"
          @show-details="showConflictDetails"
        />
      </div>
    </div>
    
    <!-- Recommendations -->
    <div v-if="recommendations.length > 0" class="conflict-recommendations">
      <h4 class="recommendations-title">
        <LightBulbIcon class="w-5 h-5 text-warning" />
        Recommendations
      </h4>
      <ul class="recommendations-list">
        <li v-for="(recommendation, index) in recommendations" :key="index" class="recommendation-item">
          {{ recommendation }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  ExclamationTriangleIcon,
  XCircleIcon,
  ExclamationCircleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  ChevronDownIcon,
  Squares2X2Icon,
  ListBulletIcon,
  LightBulbIcon
} from '@heroicons/vue/24/outline';

import ConflictCard from './ConflictCard.vue';
import type { UnifiedConflict, ConflictSeverity, ConflictAnalysisResult } from '../../services/analysis/conflicts/UnifiedConflictDetector';

// Props
interface Props {
  conflicts: UnifiedConflict[];
  conflictsBySeverity: Record<ConflictSeverity, number>;
  overallRisk: ConflictSeverity;
  recommendations: string[];
  isLoading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
});

// Emits
const emit = defineEmits<{
  resolveConflict: [conflict: UnifiedConflict];
  ignoreConflict: [conflict: UnifiedConflict];
  showConflictDetails: [conflict: UnifiedConflict];
}>();

// Reactive state
const selectedSeverity = ref('');
const selectedType = ref('');
const viewMode = ref<'grouped' | 'list'>('grouped');
const collapsedGroups = ref<Record<string, boolean>>({});

// Computed properties
const totalConflicts = computed(() => props.conflicts.length);

const riskLevelClass = computed(() => ({
  'text-error': props.overallRisk === 'critical',
  'text-warning': props.overallRisk === 'high',
  'text-info': props.overallRisk === 'medium',
  'text-success': props.overallRisk === 'low'
}));

const filteredConflicts = computed(() => {
  let filtered = [...props.conflicts];
  
  if (selectedSeverity.value) {
    filtered = filtered.filter(c => c.severity === selectedSeverity.value);
  }
  
  if (selectedType.value) {
    filtered = filtered.filter(c => c.type === selectedType.value);
  }
  
  return filtered.sort((a, b) => {
    const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    return severityOrder[b.severity] - severityOrder[a.severity];
  });
});

const groupedConflicts = computed(() => {
  const groups: Record<string, UnifiedConflict[]> = {
    critical: [],
    high: [],
    medium: [],
    low: []
  };
  
  for (const conflict of filteredConflicts.value) {
    groups[conflict.severity].push(conflict);
  }
  
  return groups;
});

// Methods
const getSeverityIcon = (severity: string) => {
  switch (severity) {
    case 'critical': return XCircleIcon;
    case 'high': return ExclamationTriangleIcon;
    case 'medium': return ExclamationCircleIcon;
    case 'low': return InformationCircleIcon;
    default: return InformationCircleIcon;
  }
};

const formatSeverity = (severity: string): string => {
  return severity.charAt(0).toUpperCase() + severity.slice(1);
};

const toggleGroup = (severity: string) => {
  collapsedGroups.value[severity] = !collapsedGroups.value[severity];
};

const resolveConflict = (conflict: UnifiedConflict) => {
  emit('resolveConflict', conflict);
};

const ignoreConflict = (conflict: UnifiedConflict) => {
  emit('ignoreConflict', conflict);
};

const showConflictDetails = (conflict: UnifiedConflict) => {
  emit('showConflictDetails', conflict);
};

// Initialize collapsed state
watch(() => props.conflicts, () => {
  // Initialize all groups as expanded
  collapsedGroups.value = {
    critical: false,
    high: false,
    medium: false,
    low: false
  };
}, { immediate: true });
</script>

<style scoped>
.conflict-visualization {
  @apply space-y-6;
}

.conflict-header {
  @apply flex items-center justify-between p-4 bg-surface rounded-lg border;
}

.header-title {
  @apply flex items-center gap-2 text-lg font-semibold;
}

.header-subtitle {
  @apply text-sm text-muted mt-1;
}

.header-actions {
  @apply flex items-center gap-3;
}

.filter-select {
  @apply px-3 py-2 border rounded bg-background text-sm;
}

.view-toggle {
  @apply flex items-center gap-1 p-1 bg-background rounded border;
}

.toggle-btn {
  @apply flex items-center gap-1 px-3 py-2 text-sm rounded transition-colors;
}

.toggle-btn:hover {
  @apply bg-surface;
}

.toggle-btn.active {
  @apply bg-primary text-primary-foreground;
}

.conflict-summary {
  @apply grid grid-cols-4 gap-4;
}

.summary-card {
  @apply flex items-center gap-3 p-4 rounded-lg border;
}

.summary-card.critical {
  @apply bg-error/5 border-error/20;
}

.summary-card.high {
  @apply bg-warning/5 border-warning/20;
}

.summary-card.medium {
  @apply bg-info/5 border-info/20;
}

.summary-card.low {
  @apply bg-success/5 border-success/20;
}

.card-icon {
  @apply flex-shrink-0;
}

.summary-card.critical .card-icon {
  @apply text-error;
}

.summary-card.high .card-icon {
  @apply text-warning;
}

.summary-card.medium .card-icon {
  @apply text-info;
}

.summary-card.low .card-icon {
  @apply text-success;
}

.card-number {
  @apply text-2xl font-bold;
}

.card-label {
  @apply text-sm text-muted;
}

.conflict-loading {
  @apply flex flex-col items-center justify-center py-12 text-center;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mb-4;
}

.conflict-empty {
  @apply flex flex-col items-center justify-center py-12 text-center;
}

.empty-icon {
  @apply w-16 h-16 mb-4;
}

.conflict-groups {
  @apply space-y-4;
}

.conflict-group {
  @apply border rounded-lg overflow-hidden;
}

.conflict-group.severity-critical {
  @apply border-error/20;
}

.conflict-group.severity-high {
  @apply border-warning/20;
}

.conflict-group.severity-medium {
  @apply border-info/20;
}

.conflict-group.severity-low {
  @apply border-success/20;
}

.group-header {
  @apply flex items-center justify-between p-4 bg-surface border-b;
}

.group-title {
  @apply flex items-center gap-2 font-medium;
}

.group-count {
  @apply text-sm text-muted;
}

.group-toggle {
  @apply p-1 rounded hover:bg-background transition-colors;
}

.group-content {
  @apply p-4 space-y-3;
}

.conflict-list {
  @apply space-y-4;
}

.conflict-recommendations {
  @apply p-4 bg-warning/5 border border-warning/20 rounded-lg;
}

.recommendations-title {
  @apply flex items-center gap-2 font-medium mb-3;
}

.recommendations-list {
  @apply space-y-2;
}

.recommendation-item {
  @apply flex items-start gap-2 text-sm;
}

.recommendation-item::before {
  content: "•";
  @apply text-warning flex-shrink-0 mt-0.5;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .conflict-summary {
    @apply grid-cols-2;
  }
  
  .conflict-header {
    @apply flex-col gap-4;
  }
  
  .header-actions {
    @apply w-full justify-between;
  }
}

@media (max-width: 640px) {
  .conflict-summary {
    @apply grid-cols-1;
  }
  
  .header-actions {
    @apply flex-col gap-3;
  }
}
</style>
