/* Shared CSS utilities for dashboard components */
/* Updated to use Simonitor Design System v4.0 */

/* Common background patterns - Updated to use design system */
.dashboard-glass {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.dashboard-glass-hover:hover {
  background: color-mix(in srgb, var(--bg-elevated) 85%, var(--primary) 15%);
  border-color: color-mix(in srgb, var(--border-light) 70%, var(--primary) 30%);
}

.dashboard-glass-focus:focus-within {
  background: color-mix(in srgb, var(--bg-elevated) 90%, var(--primary) 10%);
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-bg);
}

/* ===== DASHBOARD-SPECIFIC BUTTON EXTENSIONS ===== */
/* Extends the main button system with dashboard-specific styling */

/* Dashboard button group styling */
.dashboard-btn-group {
  display: flex;
  gap: var(--space-3);
  align-items: center;
  flex-wrap: wrap;
}

.dashboard-btn-group .btn {
  flex: 1;
  min-width: 120px;
}

/* Dashboard action bar styling */
.dashboard-action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4);
  background: var(--bg-elevated);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.dashboard-action-bar .btn-group {
  margin-left: auto;
}

/* ===== LEGACY DASHBOARD BUTTON COMPATIBILITY ===== */
/* Deprecated classes - Use .btn, .btn-primary, .btn-secondary instead */

.dashboard-btn {
  /* Legacy compatibility - inherits from modern button system */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-5);
  min-height: 44px;
  min-width: 44px;
  border-radius: var(--radius-lg);
  border: 1px solid transparent;
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  font-family: var(--font-family-sans);
  line-height: 1.2;
  text-decoration: none;
  white-space: nowrap;
  box-shadow:
    0 1px 3px 0 oklch(0.1 0 0 / 0.1),
    0 1px 2px -1px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.05);
  transition: all var(--duration-200) cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0) scale(1);
  cursor: pointer;
  position: relative;
  isolation: isolate;
}

.dashboard-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  filter: grayscale(0.3);
}

.dashboard-btn:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 4px 12px -2px oklch(0.1 0 0 / 0.15),
    0 2px 8px -2px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.1);
}

.dashboard-btn:active:not(:disabled) {
  transform: translateY(-1px) scale(1.01);
  transition-duration: var(--duration-100);
}

.dashboard-btn--primary {
  background: var(--primary);
  color: var(--primary-foreground);
  border-color: var(--primary);
}

.dashboard-btn--primary:hover:not(:disabled) {
  background: color-mix(in srgb, var(--primary) 90%, white 10%);
  border-color: color-mix(in srgb, var(--primary) 90%, white 10%);
  box-shadow:
    0 4px 12px -2px color-mix(in srgb, var(--primary) 40%, transparent 60%),
    0 2px 8px -2px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.15);
}

.dashboard-btn--secondary {
  background: var(--secondary);
  color: var(--secondary-foreground);
  border-color: var(--secondary);
}

.dashboard-btn--secondary:hover:not(:disabled) {
  background: color-mix(in srgb, var(--secondary) 90%, white 10%);
  border-color: color-mix(in srgb, var(--secondary) 90%, white 10%);
  box-shadow:
    0 4px 12px -2px color-mix(in srgb, var(--secondary) 40%, transparent 60%),
    0 2px 8px -2px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.15);
}

/* Common spacing */
.dashboard-spacing {
  margin-bottom: 16px;
}

/* Common responsive breakpoints */
@media (max-width: 480px) {
  .dashboard-mobile-stack {
    flex-direction: column;
    gap: 8px;
  }

  .dashboard-mobile-full {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .dashboard-tablet-stack {
    flex-direction: column;
    gap: 12px;
  }
}

/* Screen reader only - Removed duplicate (already in design system) */
/* This utility is now defined in simonitor-design-system.css */