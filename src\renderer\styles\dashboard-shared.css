/* Shared CSS utilities for dashboard components */

/* Common background patterns */
.dashboard-glass {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.dashboard-glass-hover:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.dashboard-glass-focus:focus-within {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(16, 185, 129, 0.5);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Common button styles */
.dashboard-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  white-space: nowrap;
}

.dashboard-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.dashboard-btn--primary {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.dashboard-btn--primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.dashboard-btn--secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.dashboard-btn--secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Common spacing */
.dashboard-spacing {
  margin-bottom: 16px;
}

/* Common responsive breakpoints */
@media (max-width: 480px) {
  .dashboard-mobile-stack {
    flex-direction: column;
    gap: 8px;
  }

  .dashboard-mobile-full {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .dashboard-tablet-stack {
    flex-direction: column;
    gap: 12px;
  }
}

/* Screen reader only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}