/* Shared CSS utilities for dashboard components */
/* Updated to use Simonitor Design System v4.0 */

/* Common background patterns - Updated to use design system */
.dashboard-glass {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.dashboard-glass-hover:hover {
  background: color-mix(in srgb, var(--bg-elevated) 85%, var(--primary) 15%);
  border-color: color-mix(in srgb, var(--border-light) 70%, var(--primary) 30%);
}

.dashboard-glass-focus:focus-within {
  background: color-mix(in srgb, var(--bg-elevated) 90%, var(--primary) 10%);
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-bg);
}

/* Common button styles - Updated to use design system */
.dashboard-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all var(--duration-200) var(--ease-out);
  border: 1px solid transparent;
  cursor: pointer;
  white-space: nowrap;
}

.dashboard-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.dashboard-btn--primary {
  background: var(--success);
  color: var(--success-foreground);
  border-color: var(--success);
  box-shadow: var(--shadow-md);
}

.dashboard-btn--primary:hover:not(:disabled) {
  background: var(--success-hover);
  border-color: var(--success-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.dashboard-btn--secondary {
  background: var(--secondary-bg);
  color: var(--secondary);
  border-color: var(--secondary-border);
  backdrop-filter: blur(10px);
}

.dashboard-btn--secondary:hover:not(:disabled) {
  background: var(--secondary);
  color: var(--secondary-foreground);
  border-color: var(--secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Common spacing */
.dashboard-spacing {
  margin-bottom: 16px;
}

/* Common responsive breakpoints */
@media (max-width: 480px) {
  .dashboard-mobile-stack {
    flex-direction: column;
    gap: 8px;
  }

  .dashboard-mobile-full {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .dashboard-tablet-stack {
    flex-direction: column;
    gap: 12px;
  }
}

/* Screen reader only - Removed duplicate (already in design system) */
/* This utility is now defined in simonitor-design-system.css */