/**
 * Simonitor Advanced Color System v6.0
 * Modern color palette implementation with exact specified colors
 * Designed for Phase 5A: Advanced Color System Implementation
 * Colors: #BBDB06, #F5BB00, #EC9F05, #D76A03, #BF3100
 */

/* ===== EXACT SPECIFIED COLOR PALETTE ===== */
/* Implementation of user-specified modern color palette */

:root {
  /* ===== PRIMARY MODERN PALETTE ===== */

  /* Primary Green - #BBDB06 - Vibrant lime green for primary actions, success states */
  /* Psychology: Growth, energy, innovation, success */
  --primary-green: #BBDB06;
  --primary-green-50: color-mix(in srgb, var(--primary-green) 10%, white);
  --primary-green-100: color-mix(in srgb, var(--primary-green) 20%, white);
  --primary-green-200: color-mix(in srgb, var(--primary-green) 40%, white);
  --primary-green-300: color-mix(in srgb, var(--primary-green) 60%, white);
  --primary-green-400: color-mix(in srgb, var(--primary-green) 80%, white);
  --primary-green-500: var(--primary-green); /* Base: #BBDB06 */
  --primary-green-600: color-mix(in srgb, var(--primary-green) 90%, black);
  --primary-green-700: color-mix(in srgb, var(--primary-green) 80%, black);
  --primary-green-800: color-mix(in srgb, var(--primary-green) 70%, black);
  --primary-green-900: color-mix(in srgb, var(--primary-green) 60%, black);
  --primary-green-950: color-mix(in srgb, var(--primary-green) 40%, black);

  /* Primary Yellow - #F5BB00 - Warm yellow for secondary actions, highlights */
  /* Psychology: Optimism, creativity, warmth, information */
  --primary-yellow: #F5BB00;
  --primary-yellow-50: color-mix(in srgb, var(--primary-yellow) 10%, white);
  --primary-yellow-100: color-mix(in srgb, var(--primary-yellow) 20%, white);
  --primary-yellow-200: color-mix(in srgb, var(--primary-yellow) 40%, white);
  --primary-yellow-300: color-mix(in srgb, var(--primary-yellow) 60%, white);
  --primary-yellow-400: color-mix(in srgb, var(--primary-yellow) 80%, white);
  --primary-yellow-500: var(--primary-yellow); /* Base: #F5BB00 */
  --primary-yellow-600: color-mix(in srgb, var(--primary-yellow) 90%, black);
  --primary-yellow-700: color-mix(in srgb, var(--primary-yellow) 80%, black);
  --primary-yellow-800: color-mix(in srgb, var(--primary-yellow) 70%, black);
  --primary-yellow-900: color-mix(in srgb, var(--primary-yellow) 60%, black);
  --primary-yellow-950: color-mix(in srgb, var(--primary-yellow) 40%, black);

  /* Primary Orange - #EC9F05 - Rich orange for warnings, attention states */
  /* Psychology: Energy, enthusiasm, attention, caution */
  --primary-orange: #EC9F05;
  --primary-orange-50: color-mix(in srgb, var(--primary-orange) 10%, white);
  --primary-orange-100: color-mix(in srgb, var(--primary-orange) 20%, white);
  --primary-orange-200: color-mix(in srgb, var(--primary-orange) 40%, white);
  --primary-orange-300: color-mix(in srgb, var(--primary-orange) 60%, white);
  --primary-orange-400: color-mix(in srgb, var(--primary-orange) 80%, white);
  --primary-orange-500: var(--primary-orange); /* Base: #EC9F05 */
  --primary-orange-600: color-mix(in srgb, var(--primary-orange) 90%, black);
  --primary-orange-700: color-mix(in srgb, var(--primary-orange) 80%, black);
  --primary-orange-800: color-mix(in srgb, var(--primary-orange) 70%, black);
  --primary-orange-900: color-mix(in srgb, var(--primary-orange) 60%, black);
  --primary-orange-950: color-mix(in srgb, var(--primary-orange) 40%, black);

  /* Primary Red-Orange - #D76A03 - Deep orange-red for important alerts, premium */
  /* Psychology: Authority, importance, premium features */
  --primary-red-orange: #D76A03;
  --primary-red-orange-50: color-mix(in srgb, var(--primary-red-orange) 10%, white);
  --primary-red-orange-100: color-mix(in srgb, var(--primary-red-orange) 20%, white);
  --primary-red-orange-200: color-mix(in srgb, var(--primary-red-orange) 40%, white);
  --primary-red-orange-300: color-mix(in srgb, var(--primary-red-orange) 60%, white);
  --primary-red-orange-400: color-mix(in srgb, var(--primary-red-orange) 80%, white);
  --primary-red-orange-500: var(--primary-red-orange); /* Base: #D76A03 */
  --primary-red-orange-600: color-mix(in srgb, var(--primary-red-orange) 90%, black);
  --primary-red-orange-700: color-mix(in srgb, var(--primary-red-orange) 80%, black);
  --primary-red-orange-800: color-mix(in srgb, var(--primary-red-orange) 70%, black);
  --primary-red-orange-900: color-mix(in srgb, var(--primary-red-orange) 60%, black);
  --primary-red-orange-950: color-mix(in srgb, var(--primary-red-orange) 40%, black);

  /* Primary Red - #BF3100 - Deep red for errors, critical states */
  /* Psychology: Danger, urgency, critical problems, stop */
  --primary-red: #BF3100;
  --primary-red-50: color-mix(in srgb, var(--primary-red) 10%, white);
  --primary-red-100: color-mix(in srgb, var(--primary-red) 20%, white);
  --primary-red-200: color-mix(in srgb, var(--primary-red) 40%, white);
  --primary-red-300: color-mix(in srgb, var(--primary-red) 60%, white);
  --primary-red-400: color-mix(in srgb, var(--primary-red) 80%, white);
  --primary-red-500: var(--primary-red); /* Base: #BF3100 */
  --primary-red-600: color-mix(in srgb, var(--primary-red) 90%, black);
  --primary-red-700: color-mix(in srgb, var(--primary-red) 80%, black);
  --primary-red-800: color-mix(in srgb, var(--primary-red) 70%, black);
  --primary-red-900: color-mix(in srgb, var(--primary-red) 60%, black);
  --primary-red-950: color-mix(in srgb, var(--primary-red) 40%, black);

  /* Supporting Neutrals - Apple-inspired minimalism */
  /* Psychology: Clean, professional, unobtrusive */
  --neutral-50: #FAFAFA;
  --neutral-100: #F5F5F5;
  --neutral-200: #E5E5E5;
  --neutral-300: #D4D4D4;
  --neutral-400: #A3A3A3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;

  /* ===== SEMANTIC COLOR MAPPING ===== */

  /* Action States - Using exact specified colors */
  --color-success: var(--primary-green);     /* #BBDB06 - Success, completion */
  --color-warning: var(--primary-orange);    /* #EC9F05 - Warnings, caution */
  --color-error: var(--primary-red);         /* #BF3100 - Errors, critical */
  --color-info: var(--primary-yellow);       /* #F5BB00 - Information, highlights */
  --color-premium: var(--primary-red-orange); /* #D76A03 - Premium features */

  /* Interactive States with Progressive Enhancement */
  --color-primary: var(--primary-green);     /* Primary buttons, links */
  --color-primary-hover: var(--primary-green-600);
  --color-primary-active: var(--primary-green-700);
  --color-primary-light: var(--primary-green-100);

  --color-secondary: var(--primary-yellow);  /* Secondary buttons */
  --color-secondary-hover: var(--primary-yellow-600);
  --color-secondary-active: var(--primary-yellow-700);
  --color-secondary-light: var(--primary-yellow-100);

  --color-accent: var(--primary-orange);     /* Accent elements, highlights */
  --color-accent-hover: var(--primary-orange-600);
  --color-accent-active: var(--primary-orange-700);
  --color-accent-light: var(--primary-orange-100);

  /* Quality Score Mapping - Semantic progression */
  --quality-excellent: var(--primary-green);    /* 90-100% quality */
  --quality-good: var(--primary-yellow);        /* 75-89% quality */
  --quality-fair: var(--primary-orange);        /* 60-74% quality */
  --quality-poor: var(--primary-red);           /* Below 60% quality */

  /* Mod Intelligence Type Colors */
  --intelligence-script: var(--primary-red-orange);  /* Script intelligence */
  --intelligence-resource: var(--primary-green);     /* Resource intelligence */
  --intelligence-quality: var(--primary-yellow);     /* Quality assessment */
  --intelligence-dependency: var(--primary-orange);  /* Dependency analysis */

  /* File Type Colors - Semantic mapping */
  --file-package: var(--primary-green);         /* .package files - Primary */
  --file-script: var(--primary-red-orange);     /* .ts4script files - Premium */
  --file-resource: var(--primary-orange);       /* resource files - Warning */
  --file-unknown: var(--primary-red);           /* unknown/problematic - Error */

  /* Background Colors - Dark theme hierarchy */
  --bg-primary: var(--neutral-900);
  --bg-secondary: var(--neutral-800);
  --bg-elevated: var(--neutral-700);
  --bg-glass: color-mix(in srgb, var(--neutral-800) 80%, transparent);

  /* Text Colors - Light text for dark background */
  --text-primary: var(--neutral-50);
  --text-secondary: var(--neutral-300);
  --text-muted: var(--neutral-400);
  --text-inverse: var(--neutral-900);
  --text-on-primary: #FFFFFF;
  --text-on-secondary: var(--neutral-900);

  /* Border Colors - Subtle hierarchy */
  --border-light: var(--neutral-200);
  --border-medium: var(--neutral-300);
  --border-strong: var(--neutral-400);

  /* Focus Ring - Accessibility compliant */
  --focus-ring: var(--color-primary);
  --focus-ring-offset: var(--neutral-50);

  /* Interactive State Colors with Progressive Enhancement */
  --hover-overlay: color-mix(in srgb, var(--neutral-900) 5%, transparent);
  --active-overlay: color-mix(in srgb, var(--neutral-900) 10%, transparent);
  --disabled-overlay: color-mix(in srgb, var(--neutral-500) 50%, transparent);
}

/* ===== DARK MODE VARIANTS ===== */
.dark,
[data-theme="dark"] {
  /* Dark mode background adjustments */
  --bg-primary: var(--neutral-900);
  --bg-secondary: var(--neutral-800);
  --bg-elevated: var(--neutral-700);
  --bg-glass: color-mix(in srgb, var(--neutral-800) 80%, transparent);

  /* Dark mode text adjustments */
  --text-primary: var(--neutral-50);
  --text-secondary: var(--neutral-300);
  --text-muted: var(--neutral-400);
  --text-inverse: var(--neutral-900);

  /* Dark mode border adjustments */
  --border-light: var(--neutral-700);
  --border-medium: var(--neutral-600);
  --border-strong: var(--neutral-500);

  /* Dark mode focus ring */
  --focus-ring-offset: var(--neutral-900);

  /* Dark mode interactive overlays */
  --hover-overlay: color-mix(in srgb, var(--neutral-50) 5%, transparent);
  --active-overlay: color-mix(in srgb, var(--neutral-50) 10%, transparent);
}

/* ===== HIGH CONTRAST MODE ===== */
.high-contrast,
[data-theme="high-contrast"],
@media (prefers-contrast: high) {
  :root {
    /* Enhanced contrast for accessibility */
    --border-light: var(--neutral-600);
    --border-medium: var(--neutral-700);
    --border-strong: var(--neutral-800);

    /* Higher contrast text */
    --text-secondary: var(--neutral-700);
    --text-muted: var(--neutral-600);

    /* Enhanced focus indicators */
    --focus-ring: var(--primary-red-orange);
  }
}

/* ===== REDUCED MOTION SUPPORT ===== */
.reduced-motion,
@media (prefers-reduced-motion: reduce) {
  :root {
    /* Disable transitions for accessibility */
    --transition-duration: 0ms;
    --animation-duration: 0ms;
  }
}

/* ===== PROGRESSIVE INTERACTION STATES ===== */
/* Modern CSS with color-mix for smooth transitions */
:root {
  /* Transition timing for smooth interactions */
  --transition-duration: 200ms;
  --transition-easing: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-duration: 300ms;

  /* Shadow system for depth */
  --shadow-sm: 0 1px 2px 0 color-mix(in srgb, var(--neutral-900) 5%, transparent);
  --shadow-md: 0 4px 6px -1px color-mix(in srgb, var(--neutral-900) 10%, transparent);
  --shadow-lg: 0 10px 15px -3px color-mix(in srgb, var(--neutral-900) 10%, transparent);
  --shadow-xl: 0 20px 25px -5px color-mix(in srgb, var(--neutral-900) 10%, transparent);
}
