<template>
  <div class="dashboard-stats">
    <div class="stats-container">
      <!-- Main Results Count -->
      <div class="results-info">
        <span class="results-count">{{ filteredCount }}</span>
        <span class="results-text">mods</span>
        <span v-if="hasActiveFilters" class="results-filtered">(filtered)</span>
      </div>

      <!-- Additional Statistics -->
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-label">Total</span>
          <span class="stat-value">{{ totalCount }}</span>
        </div>

        <div v-if="hasActiveFilters" class="stat-item">
          <span class="stat-label">Filtered</span>
          <span class="stat-value">{{ filteredCount }}</span>
        </div>

        <div v-if="totalPages > 1" class="stat-item">
          <span class="stat-label">Pages</span>
          <span class="stat-value">{{ totalPages }}</span>
        </div>

        <div v-if="totalPages > 1" class="stat-item">
          <span class="stat-label">Current Page</span>
          <span class="stat-value">{{ currentPage }}</span>
        </div>

        <div class="stat-item">
          <span class="stat-label">Per Page</span>
          <span class="stat-value">{{ itemsPerPage }}</span>
        </div>
      </div>

      <!-- Pagination Info (if applicable) -->
      <div v-if="totalPages > 1" class="pagination-info">
        <span class="pagination-text">
          Showing {{ startIndex + 1 }}-{{ endIndex }} of {{ filteredCount }} mods
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface DashboardStatsProps {
  totalCount: number;
  filteredCount: number;
  currentPage: number;
  itemsPerPage: number;
  hasActiveFilters: boolean;
}

// Props
const props = defineProps<DashboardStatsProps>();

// Computed properties
const totalPages = computed(() =>
  Math.ceil(props.filteredCount / props.itemsPerPage)
);

const startIndex = computed(() =>
  (props.currentPage - 1) * props.itemsPerPage
);

const endIndex = computed(() =>
  Math.min(startIndex.value + props.itemsPerPage, props.filteredCount)
);
</script>

<style scoped>
/* Import shared design system styles */
@import '../../styles/dashboard-shared.css';
@import '../../styles/simonitor-design-system.css';

.dashboard-stats {
  margin-bottom: 16px;
}

.stats-container {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);
}

.results-info {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 12px;
  justify-content: center;
}

.results-count {
  font-size: 2rem;
  font-weight: 700;
  color: var(--success);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  line-height: 1;
  letter-spacing: -0.025em;
}

.results-text {
  font-size: 1.1rem;
  color: var(--text-primary);
  font-weight: 500;
}

.results-filtered {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-style: italic;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 12px;
  margin-bottom: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-light);
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-muted);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-value {
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: 600;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.pagination-info {
  text-align: center;
  padding-top: 8px;
  border-top: 1px solid var(--border-light);
}

.pagination-text {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .stats-container {
    padding: 12px;
  }

  .results-count {
    font-size: 1.75rem;
  }

  .results-text {
    font-size: 1rem;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
    gap: 8px;
  }

  .stat-item {
    padding: 6px;
  }

  .stat-label {
    font-size: 0.7rem;
  }

  .stat-value {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .stats-container {
    padding: 8px;
  }

  .results-info {
    flex-direction: column;
    gap: 4px;
    margin-bottom: 8px;
  }

  .results-count {
    font-size: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }

  .pagination-text {
    font-size: 0.8rem;
  }
}
</style>