/**
 * Central ModData type definition for Simonitor
 * 
 * This file defines the core data structure for mod information
 * used throughout the application, including support for multiple thumbnails.
 */

import type { ThumbnailData } from '../services/visual/ThumbnailExtractionService';

export interface ModData {
  // Basic file information
  id?: string;
  fileName: string;
  filePath?: string;
  fileExtension: string;
  fileSize: number;
  lastModified?: number;
  
  // Metadata
  author?: string;
  version?: string;
  description?: string;
  metadataConfidence?: number;
  
  // Analysis data
  resourceCount?: number;
  category?: string;
  subcategory?: string;
  processingTime: number;
  
  // Thumbnail data - ENHANCED for multiple thumbnails
  thumbnailData?: string; // Legacy single thumbnail (base64)
  thumbnailUrl?: string; // Legacy single thumbnail URL
  thumbnails?: ThumbnailData[]; // NEW: Array of all extracted thumbnails
  primaryThumbnail?: ThumbnailData; // NEW: Primary/featured thumbnail
  thumbnailVariations?: ThumbnailData[]; // NEW: Color/style variations
  
  // Dependencies and quality
  dependencyData?: any;
  qualityAssessmentData?: any;
  
  // Phase 1: StringTable Analysis Fields
  stringTableData?: {
    modName?: string;
    description?: string;
    itemNames: string[];
    customStringCount: number;
    locale: string;
    confidence: number;
    processingTime: number;
  };
  actualModName?: string;
  actualDescription?: string;
  extractedItemNames?: string[];
  hasStringTable?: boolean;
  
  // Enhanced CAS Content Analysis Fields
  casContent?: {
    totalItems: number;
    items: Array<{
      category: string;
      subcategory: string;
      description: string;
      isHair: boolean;
      isClothing: boolean;
      isMakeup: boolean;
      isAccessory: boolean;
      hairDetails?: {
        length: string;
        style: string[];
        texture: string;
        hasAccessories: boolean;
        confidence: number;
        detectionMethod: string;
        keywords: string[];
      };
      tags: string[];
    }>;
  };
  
  // Object classification
  objectClassification?: {
    category: string;
    subcategory: string;
    confidence: number;
    detectionMethod: string;
    keywords: string[];
    metadata: any;
    tags: string[];
  };
  
  // Universal classification
  universalClassification?: {
    category: string;
    subcategories: string[];
    confidence: number;
    detectionMethod: string;
    keywords: string[];
    metadata: any;
    tags: string[];
  };
}

/**
 * Extended ModData interface for visual browsing with enhanced thumbnail support
 */
export interface VisualModData extends ModData {
  thumbnails: ThumbnailData[];
  primaryThumbnail?: ThumbnailData;
  thumbnailVariations: ThumbnailData[]; // Color/style variations (e.g., 25 hair colors)
  visualTags: string[];
  colorPalette?: string[];
  visualCategory: string;
  visualSubcategory?: string;
  thumbnailGenerationStatus: 'pending' | 'processing' | 'completed' | 'failed';
  thumbnailGenerationError?: string;
  hasMultipleVariations?: boolean; // Flag for UI to show variation controls
}

/**
 * Utility type for thumbnail display modes
 */
export type ThumbnailDisplayMode = 'single' | 'grid' | 'carousel' | 'expandable';

/**
 * Configuration for thumbnail variation display
 */
export interface ThumbnailVariationConfig {
  displayMode: ThumbnailDisplayMode;
  maxVisibleVariations: number;
  showVariationCount: boolean;
  enableHoverPreview: boolean;
  groupByCategory: boolean;
}
