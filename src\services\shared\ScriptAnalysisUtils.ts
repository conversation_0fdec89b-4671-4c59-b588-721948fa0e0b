/**
 * Shared Script Analysis Utilities
 * 
 * Consolidates script analysis logic to eliminate duplication across services.
 * Provides standardized import detection, dependency analysis, and syntax validation.
 */

export interface ImportInfo {
    module: string;
    importType: 'import' | 'from_import';
    items: string[];
    line: number;
    isRelative: boolean;
}

export interface FrameworkDependency {
    name: string;
    type: 'framework' | 'library' | 'mod';
    confidence: number;
    detectionMethod: string;
    requiredVersion?: string;
    downloadUrl?: string;
}

export interface SyntaxValidationResult {
    isValid: boolean;
    errors: SyntaxError[];
    warnings: SyntaxWarning[];
    complexity: 'low' | 'medium' | 'high';
    lineCount: number;
}

export interface SyntaxError {
    line: number;
    column?: number;
    message: string;
    severity: 'error' | 'warning';
}

export interface SyntaxWarning {
    line: number;
    message: string;
    suggestion: string;
}

/**
 * Utility class for script analysis operations
 */
export class ScriptAnalysisUtils {
    // Pre-compiled regex patterns for performance
    private static readonly IMPORT_PATTERNS = {
        SIMPLE_IMPORT: /^import\s+([\w.]+)(?:\s+as\s+(\w+))?/gm,
        FROM_IMPORT: /^from\s+([\w.]+)\s+import\s+(.+)/gm,
        RELATIVE_IMPORT: /^from\s+(\.+[\w.]*)\s+import\s+(.+)/gm
    };

    private static readonly FRAMEWORK_PATTERNS = [
        {
            name: 'Sims 4 Community Library',
            patterns: [
                /from\s+sims4communitylib/gi,
                /import\s+sims4communitylib/gi,
                /from\s+s4cl/gi,
                /import\s+s4cl/gi,
                /sims4communitylib\./gi
            ],
            type: 'framework' as const,
            downloadUrl: 'https://github.com/ColonolNutty/Sims4CommunityLibrary'
        },
        {
            name: 'Lot 51 Core Library',
            patterns: [
                /from\s+lot51/gi,
                /import\s+lot51/gi,
                /lot51\./gi,
                /lot51_core/gi
            ],
            type: 'framework' as const,
            downloadUrl: 'https://www.patreon.com/lot51cc'
        },
        {
            name: 'WickedWhims',
            patterns: [
                /from\s+wickedwhims/gi,
                /import\s+wickedwhims/gi,
                /from\s+ww_/gi,
                /wickedwhims\./gi
            ],
            type: 'framework' as const,
            downloadUrl: 'https://wickedwhimsmod.com'
        },
        {
            name: 'Basemental Drugs',
            patterns: [
                /from\s+basemental/gi,
                /import\s+basemental/gi,
                /basemental\./gi
            ],
            type: 'framework' as const,
            downloadUrl: 'https://basementalcc.com'
        },
        {
            name: 'MC Command Center',
            patterns: [
                /from\s+mccc/gi,
                /import\s+mccc/gi,
                /mc_cmd_center/gi,
                /mc_command_center/gi
            ],
            type: 'framework' as const,
            downloadUrl: 'https://deaderpoolmc.wixsite.com/mccc'
        },
        {
            name: 'XML Injector',
            patterns: [
                /xml_injector/gi,
                /xmlinjector/gi
            ],
            type: 'framework' as const,
            downloadUrl: 'https://scumbumbomods.com'
        },
        {
            name: 'UI Cheats Extension',
            patterns: [
                /ui_cheats/gi,
                /uicheats/gi
            ],
            type: 'mod' as const,
            downloadUrl: 'https://www.patreon.com/weerbesu'
        },
        {
            name: 'Slice of Life',
            patterns: [
                /slice_of_life/gi,
                /sol_/gi
            ],
            type: 'mod' as const,
            downloadUrl: 'https://www.kawaiistaciemods.com'
        },
        {
            name: 'Meaningful Stories',
            patterns: [
                /meaningful_stories/gi,
                /ms_/gi
            ],
            type: 'mod' as const,
            downloadUrl: 'https://www.patreon.com/roBurky'
        },
        {
            name: 'BrazenLotus Framework',
            patterns: [
                /brazenlotus/gi,
                /brazen_lotus/gi
            ],
            type: 'framework' as const,
            downloadUrl: 'https://www.patreon.com/brazenlotus'
        }
    ];

    private static readonly SYNTAX_ERROR_PATTERNS = [
        {
            pattern: /SyntaxError/gi,
            message: 'Python syntax error detected in script'
        },
        {
            pattern: /IndentationError/gi,
            message: 'Python indentation error detected in script'
        },
        {
            pattern: /TabError/gi,
            message: 'Mixed tabs and spaces detected'
        },
        {
            pattern: /NameError/gi,
            message: 'Name error detected in script'
        }
    ];

    /**
     * Extracts import information from script content
     */
    public static extractImports(content: string): ImportInfo[] {
        const imports: ImportInfo[] = [];
        const lines = content.split('\n');

        // Reset regex lastIndex
        Object.values(this.IMPORT_PATTERNS).forEach(pattern => pattern.lastIndex = 0);

        // Extract simple imports
        let match;
        while ((match = this.IMPORT_PATTERNS.SIMPLE_IMPORT.exec(content)) !== null) {
            const lineNumber = this.getLineNumber(content, match.index);
            imports.push({
                module: match[1],
                importType: 'import',
                items: [match[2] || match[1]], // Use alias if available
                line: lineNumber,
                isRelative: false
            });
        }

        // Extract from imports
        this.IMPORT_PATTERNS.FROM_IMPORT.lastIndex = 0;
        while ((match = this.IMPORT_PATTERNS.FROM_IMPORT.exec(content)) !== null) {
            const lineNumber = this.getLineNumber(content, match.index);
            const items = match[2].split(',').map(item => item.trim());
            imports.push({
                module: match[1],
                importType: 'from_import',
                items,
                line: lineNumber,
                isRelative: false
            });
        }

        // Extract relative imports
        this.IMPORT_PATTERNS.RELATIVE_IMPORT.lastIndex = 0;
        while ((match = this.IMPORT_PATTERNS.RELATIVE_IMPORT.exec(content)) !== null) {
            const lineNumber = this.getLineNumber(content, match.index);
            const items = match[2].split(',').map(item => item.trim());
            imports.push({
                module: match[1],
                importType: 'from_import',
                items,
                line: lineNumber,
                isRelative: true
            });
        }

        return imports;
    }

    /**
     * Detects framework dependencies in script content
     */
    public static detectFrameworkDependencies(content: string): FrameworkDependency[] {
        const dependencies: FrameworkDependency[] = [];

        for (const framework of this.FRAMEWORK_PATTERNS) {
            let confidence = 0;
            let detectionMethods: string[] = [];

            for (const pattern of framework.patterns) {
                pattern.lastIndex = 0; // Reset regex
                const matches = content.match(pattern);
                if (matches) {
                    confidence += matches.length * 20; // 20 points per match
                    detectionMethods.push('pattern_match');
                }
            }

            if (confidence > 0) {
                dependencies.push({
                    name: framework.name,
                    type: framework.type,
                    confidence: Math.min(100, confidence),
                    detectionMethod: detectionMethods.join(', '),
                    downloadUrl: framework.downloadUrl
                });
            }
        }

        return dependencies;
    }

    /**
     * Validates Python syntax in script content
     */
    public static validatePythonSyntax(content: string): SyntaxValidationResult {
        const result: SyntaxValidationResult = {
            isValid: true,
            errors: [],
            warnings: [],
            complexity: 'low',
            lineCount: content.split('\n').length
        };

        const lines = content.split('\n');

        // Check for syntax error patterns
        for (const errorPattern of this.SYNTAX_ERROR_PATTERNS) {
            errorPattern.pattern.lastIndex = 0;
            const matches = content.match(errorPattern.pattern);
            if (matches) {
                result.isValid = false;
                result.errors.push({
                    line: 0, // Would need more sophisticated parsing for exact line
                    message: errorPattern.message,
                    severity: 'error'
                });
            }
        }

        // Check for common issues
        this.checkCommonIssues(lines, result);

        // Assess complexity
        result.complexity = this.assessComplexity(content, lines);

        return result;
    }

    /**
     * Extracts function and class definitions from script
     */
    public static extractDefinitions(content: string): {
        functions: Array<{ name: string; line: number; args: string[] }>;
        classes: Array<{ name: string; line: number; methods: string[] }>;
    } {
        const functions: Array<{ name: string; line: number; args: string[] }> = [];
        const classes: Array<{ name: string; line: number; methods: string[] }> = [];
        const lines = content.split('\n');

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // Extract function definitions
            const funcMatch = line.match(/^def\s+(\w+)\s*\(([^)]*)\)/);
            if (funcMatch) {
                const args = funcMatch[2] ? funcMatch[2].split(',').map(arg => arg.trim()) : [];
                functions.push({
                    name: funcMatch[1],
                    line: i + 1,
                    args
                });
            }

            // Extract class definitions
            const classMatch = line.match(/^class\s+(\w+)/);
            if (classMatch) {
                const methods: string[] = [];
                
                // Look for methods in the class (simplified)
                for (let j = i + 1; j < lines.length; j++) {
                    const methodLine = lines[j];
                    if (methodLine.match(/^\s*def\s+(\w+)/)) {
                        const methodMatch = methodLine.match(/^\s*def\s+(\w+)/);
                        if (methodMatch) {
                            methods.push(methodMatch[1]);
                        }
                    } else if (methodLine.match(/^class\s+/) || methodLine.match(/^def\s+/)) {
                        break; // End of class
                    }
                }

                classes.push({
                    name: classMatch[1],
                    line: i + 1,
                    methods
                });
            }
        }

        return { functions, classes };
    }

    /**
     * Checks for common Python issues
     */
    private static checkCommonIssues(lines: string[], result: SyntaxValidationResult): void {
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const lineNumber = i + 1;

            // Check for mixed tabs and spaces
            if (line.includes('\t') && line.includes('    ')) {
                result.warnings.push({
                    line: lineNumber,
                    message: 'Mixed tabs and spaces detected',
                    suggestion: 'Use consistent indentation (prefer spaces)'
                });
            }

            // Check for very long lines
            if (line.length > 120) {
                result.warnings.push({
                    line: lineNumber,
                    message: 'Line is very long',
                    suggestion: 'Consider breaking long lines for readability'
                });
            }

            // Check for TODO/FIXME comments
            if (line.includes('TODO') || line.includes('FIXME')) {
                result.warnings.push({
                    line: lineNumber,
                    message: 'TODO/FIXME comment found',
                    suggestion: 'Code may be incomplete'
                });
            }
        }
    }

    /**
     * Assesses script complexity
     */
    private static assessComplexity(content: string, lines: string[]): 'low' | 'medium' | 'high' {
        let complexityScore = 0;

        // Count functions and classes
        const functionCount = (content.match(/^def\s+\w+/gm) || []).length;
        const classCount = (content.match(/^class\s+\w+/gm) || []).length;
        
        complexityScore += functionCount * 2;
        complexityScore += classCount * 5;

        // Count control structures
        const controlStructures = (content.match(/^\s*(if|for|while|try|with)\s+/gm) || []).length;
        complexityScore += controlStructures;

        // Count imports
        const importCount = (content.match(/^(import|from)\s+/gm) || []).length;
        complexityScore += importCount;

        // Line count factor
        complexityScore += Math.floor(lines.length / 50);

        if (complexityScore < 10) return 'low';
        if (complexityScore < 30) return 'medium';
        return 'high';
    }

    /**
     * Gets line number from character index
     */
    private static getLineNumber(content: string, index: number): number {
        return content.substring(0, index).split('\n').length;
    }

    /**
     * Checks if script has main execution block
     */
    public static hasMainBlock(content: string): boolean {
        return /if\s+__name__\s*==\s*['"']__main__['"']/m.test(content);
    }

    /**
     * Extracts docstrings from script
     */
    public static extractDocstrings(content: string): Array<{ type: 'module' | 'function' | 'class'; content: string; line: number }> {
        const docstrings: Array<{ type: 'module' | 'function' | 'class'; content: string; line: number }> = [];
        
        // This would need more sophisticated parsing for accurate docstring extraction
        // For now, return empty array as placeholder
        
        return docstrings;
    }

    /**
     * Estimates script performance impact
     */
    public static estimatePerformanceImpact(content: string): 'low' | 'medium' | 'high' {
        let impactScore = 0;

        // Check for potentially expensive operations
        const expensivePatterns = [
            /\.find\(/g,
            /\.filter\(/g,
            /for\s+\w+\s+in\s+.*:/g,
            /while\s+.*:/g,
            /time\.sleep\(/g,
            /threading\./g
        ];

        for (const pattern of expensivePatterns) {
            const matches = content.match(pattern);
            if (matches) {
                impactScore += matches.length;
            }
        }

        if (impactScore < 5) return 'low';
        if (impactScore < 15) return 'medium';
        return 'high';
    }
}
