/**
 * Unified Error Handler
 * 
 * Consolidates all error handling across Simonitor to eliminate the three
 * different error handling systems currently in use.
 */

/**
 * Unified error severity levels (consolidates all existing systems)
 */
export enum UnifiedErrorSeverity {
    CRITICAL = 'critical',  // System-breaking errors
    HIGH = 'high',         // Major functionality issues  
    MEDIUM = 'medium',     // Moderate issues
    LOW = 'low',           // Minor issues/warnings
    INFO = 'info'          // Informational messages
}

/**
 * Unified error categories (expanded from ErrorHandler.ts)
 */
export enum UnifiedErrorCategory {
    // File and package errors
    PACKAGE_LOADING = 'package_loading',
    PACKAGE_STRUCTURE = 'package_structure', 
    FILE_VALIDATION = 'file_validation',
    CORRUPTED_PACKAGE = 'corrupted_package',
    
    // Resource processing errors
    RESOURCE_PARSING = 'resource_parsing',
    RESOURCE_EXTRACTION = 'resource_extraction',
    INVALID_RESOURCES = 'invalid_resources',
    
    // Script and content errors
    SCRIPT_ANALYSIS = 'script_analysis',
    PYTHON_SYNTAX = 'python_syntax',
    DEPENDENCY_RESOLUTION = 'dependency_resolution',
    
    // Technical errors
    COMPRESSION = 'compression',
    HASHING = 'hashing',
    XML_PARSING = 'xml_parsing',
    IMAGE_PROCESSING = 'image_processing',
    
    // Analysis errors
    CONTENT_ANALYSIS = 'content_analysis',
    CONFLICT_DETECTION = 'conflict_detection',
    METADATA_EXTRACTION = 'metadata_extraction',
    
    // System errors
    PERFORMANCE = 'performance',
    MEMORY = 'memory',
    TIMEOUT = 'timeout',
    UNKNOWN = 'unknown'
}

/**
 * Unified error information structure
 */
export interface UnifiedErrorInfo {
    id: string;                           // Unique error ID
    category: UnifiedErrorCategory;       // Error category
    severity: UnifiedErrorSeverity;       // Error severity
    message: string;                      // Human-readable message
    context: string;                      // Where the error occurred
    filePath?: string;                    // File being processed
    timestamp: string;                    // When the error occurred
    suggestion?: string;                  // How to fix the error
    autoFixAvailable: boolean;            // Can be automatically fixed
    autoDeleteRecommended: boolean;       // Should file be deleted
    recoverable: boolean;                 // Can processing continue
    errorCode: string;                    // Unique error code
    originalError?: any;                  // Original error object
    confidence: number;                   // Confidence in error detection (0-100)
}

/**
 * Unified error handler that consolidates all error handling systems
 */
export class UnifiedErrorHandler {
    private static errorCount = 0;

    /**
     * Creates a unified error from any error source
     */
    public static createError(
        error: any,
        context: string,
        filePath?: string,
        category?: UnifiedErrorCategory
    ): UnifiedErrorInfo {
        const errorId = this.generateErrorId();
        const detectedCategory = category || this.categorizeError(error);
        const severity = this.determineSeverity(detectedCategory, error);
        
        return {
            id: errorId,
            category: detectedCategory,
            severity,
            message: this.extractMessage(error),
            context,
            filePath,
            timestamp: new Date().toISOString(),
            suggestion: this.getSuggestion(detectedCategory, error),
            autoFixAvailable: this.canAutoFix(detectedCategory),
            autoDeleteRecommended: this.shouldAutoDelete(severity, detectedCategory),
            recoverable: this.isRecoverable(detectedCategory),
            errorCode: this.generateErrorCode(detectedCategory, severity),
            originalError: error,
            confidence: this.calculateConfidence(detectedCategory, error)
        };
    }

    /**
     * Converts legacy error formats to unified format
     */
    public static convertLegacyError(legacyError: any, source: 'validation' | 'broken_cc' | 's4tk'): UnifiedErrorInfo {
        switch (source) {
            case 'validation':
                return this.convertValidationError(legacyError);
            case 'broken_cc':
                return this.convertBrokenCCError(legacyError);
            case 's4tk':
                return this.convertS4TKError(legacyError);
            default:
                return this.createError(legacyError, 'legacy_conversion');
        }
    }

    /**
     * Categorizes errors based on type and message
     */
    private static categorizeError(error: any): UnifiedErrorCategory {
        const message = this.extractMessage(error).toLowerCase();
        const errorType = error?.constructor?.name?.toLowerCase() || '';

        // Package and file errors
        if (message.includes('empty') || message.includes('file too small')) {
            return UnifiedErrorCategory.FILE_VALIDATION;
        }
        if (message.includes('package') || message.includes('dbpf')) {
            return UnifiedErrorCategory.PACKAGE_LOADING;
        }
        if (message.includes('corrupted') || message.includes('invalid signature')) {
            return UnifiedErrorCategory.CORRUPTED_PACKAGE;
        }

        // Script errors
        if (message.includes('python') || message.includes('syntax')) {
            return UnifiedErrorCategory.PYTHON_SYNTAX;
        }
        if (message.includes('import') || message.includes('dependency')) {
            return UnifiedErrorCategory.DEPENDENCY_RESOLUTION;
        }

        // Resource errors
        if (message.includes('resource') || message.includes('simdata')) {
            return UnifiedErrorCategory.RESOURCE_PARSING;
        }

        // Technical errors
        if (message.includes('compression') || message.includes('zlib')) {
            return UnifiedErrorCategory.COMPRESSION;
        }
        if (message.includes('xml') || message.includes('tuning')) {
            return UnifiedErrorCategory.XML_PARSING;
        }
        if (message.includes('image') || message.includes('dds')) {
            return UnifiedErrorCategory.IMAGE_PROCESSING;
        }

        // Performance errors
        if (message.includes('timeout') || message.includes('memory')) {
            return UnifiedErrorCategory.PERFORMANCE;
        }

        return UnifiedErrorCategory.UNKNOWN;
    }

    /**
     * Determines error severity based on category and context
     */
    private static determineSeverity(category: UnifiedErrorCategory, error: any): UnifiedErrorSeverity {
        const message = this.extractMessage(error).toLowerCase();

        switch (category) {
            case UnifiedErrorCategory.CORRUPTED_PACKAGE:
            case UnifiedErrorCategory.PACKAGE_LOADING:
                return message.includes('empty') ? UnifiedErrorSeverity.CRITICAL : UnifiedErrorSeverity.HIGH;

            case UnifiedErrorCategory.FILE_VALIDATION:
                return message.includes('empty') ? UnifiedErrorSeverity.CRITICAL : UnifiedErrorSeverity.MEDIUM;

            case UnifiedErrorCategory.PYTHON_SYNTAX:
            case UnifiedErrorCategory.DEPENDENCY_RESOLUTION:
                return UnifiedErrorSeverity.HIGH;

            case UnifiedErrorCategory.RESOURCE_PARSING:
            case UnifiedErrorCategory.XML_PARSING:
                return UnifiedErrorSeverity.MEDIUM;

            case UnifiedErrorCategory.IMAGE_PROCESSING:
            case UnifiedErrorCategory.METADATA_EXTRACTION:
                return UnifiedErrorSeverity.LOW;

            case UnifiedErrorCategory.PERFORMANCE:
                return UnifiedErrorSeverity.INFO;

            default:
                return UnifiedErrorSeverity.MEDIUM;
        }
    }

    /**
     * Provides context-aware suggestions for error resolution
     */
    private static getSuggestion(category: UnifiedErrorCategory, error: any): string | undefined {
        switch (category) {
            case UnifiedErrorCategory.CORRUPTED_PACKAGE:
                return 'Re-download the mod file from the original source';
            case UnifiedErrorCategory.FILE_VALIDATION:
                return 'Verify the file downloaded completely and is not corrupted';
            case UnifiedErrorCategory.PYTHON_SYNTAX:
                return 'Contact the mod creator for a fixed version';
            case UnifiedErrorCategory.DEPENDENCY_RESOLUTION:
                return 'Install required framework mods (check mod description)';
            case UnifiedErrorCategory.COMPRESSION:
                return 'Try extracting with Sims 4 Studio first';
            case UnifiedErrorCategory.PERFORMANCE:
                return 'Try processing smaller batches or restart the application';
            default:
                return 'Check file integrity and try again';
        }
    }

    /**
     * Determines if error can be automatically fixed
     */
    private static canAutoFix(category: UnifiedErrorCategory): boolean {
        return [
            UnifiedErrorCategory.METADATA_EXTRACTION,
            UnifiedErrorCategory.IMAGE_PROCESSING
        ].includes(category);
    }

    /**
     * Determines if file should be automatically deleted
     */
    private static shouldAutoDelete(severity: UnifiedErrorSeverity, category: UnifiedErrorCategory): boolean {
        return severity === UnifiedErrorSeverity.CRITICAL && [
            UnifiedErrorCategory.CORRUPTED_PACKAGE,
            UnifiedErrorCategory.FILE_VALIDATION
        ].includes(category);
    }

    /**
     * Determines if error is recoverable
     */
    private static isRecoverable(category: UnifiedErrorCategory): boolean {
        return ![
            UnifiedErrorCategory.CORRUPTED_PACKAGE,
            UnifiedErrorCategory.PACKAGE_LOADING
        ].includes(category);
    }

    /**
     * Generates unique error ID
     */
    private static generateErrorId(): string {
        return `err-${Date.now()}-${++this.errorCount}`;
    }

    /**
     * Generates standardized error code
     */
    private static generateErrorCode(category: UnifiedErrorCategory, severity: UnifiedErrorSeverity): string {
        const categoryCode = category.toUpperCase().replace(/_/g, '');
        const severityCode = severity.toUpperCase();
        return `${categoryCode}_${severityCode}`;
    }

    /**
     * Calculates confidence in error detection
     */
    private static calculateConfidence(category: UnifiedErrorCategory, error: any): number {
        if (category === UnifiedErrorCategory.UNKNOWN) return 50;
        if (error?.message?.includes('specific error pattern')) return 95;
        return 85; // Default confidence
    }

    /**
     * Extracts message from various error formats
     */
    private static extractMessage(error: any): string {
        if (typeof error === 'string') return error;
        if (error?.message) return error.message;
        if (error?.description) return error.description;
        return 'Unknown error occurred';
    }

    /**
     * Converts validation service errors
     */
    private static convertValidationError(validationError: any): UnifiedErrorInfo {
        const severityMap = {
            'critical': UnifiedErrorSeverity.CRITICAL,
            'high': UnifiedErrorSeverity.HIGH,
            'medium': UnifiedErrorSeverity.MEDIUM,
            'low': UnifiedErrorSeverity.LOW
        };

        return this.createError(
            validationError,
            'file_validation',
            undefined,
            this.mapValidationCodeToCategory(validationError.code)
        );
    }

    /**
     * Converts broken CC service errors
     */
    private static convertBrokenCCError(brokenCCError: any): UnifiedErrorInfo {
        const severityMap = {
            'CRITICAL': UnifiedErrorSeverity.CRITICAL,
            'HIGH': UnifiedErrorSeverity.HIGH,
            'MEDIUM': UnifiedErrorSeverity.MEDIUM,
            'LOW': UnifiedErrorSeverity.LOW
        };

        return this.createError(
            brokenCCError,
            'broken_cc_detection',
            undefined,
            this.mapBrokenCCTypeToCategory(brokenCCError.type)
        );
    }

    /**
     * Converts S4TK error handler errors
     */
    private static convertS4TKError(s4tkError: any): UnifiedErrorInfo {
        return this.createError(
            s4tkError.originalError || s4tkError,
            s4tkError.context || 's4tk_operation',
            s4tkError.filePath,
            this.mapS4TKCategoryToUnified(s4tkError.category)
        );
    }

    /**
     * Maps validation error codes to unified categories
     */
    private static mapValidationCodeToCategory(code: string): UnifiedErrorCategory {
        const mapping = {
            'EMPTY_FILE': UnifiedErrorCategory.FILE_VALIDATION,
            'PACKAGE_STRUCTURE_ERROR': UnifiedErrorCategory.PACKAGE_STRUCTURE,
            'PYTHON_SYNTAX_ERROR': UnifiedErrorCategory.PYTHON_SYNTAX,
            'NULL_BYTE_CORRUPTION': UnifiedErrorCategory.CORRUPTED_PACKAGE
        };
        return mapping[code] || UnifiedErrorCategory.UNKNOWN;
    }

    /**
     * Maps broken CC types to unified categories
     */
    private static mapBrokenCCTypeToCategory(type: string): UnifiedErrorCategory {
        const mapping = {
            'CORRUPTED_PACKAGE': UnifiedErrorCategory.CORRUPTED_PACKAGE,
            'INCOMPLETE_DOWNLOAD': UnifiedErrorCategory.FILE_VALIDATION,
            'INVALID_RESOURCES': UnifiedErrorCategory.INVALID_RESOURCES,
            'MALFORMED_XML': UnifiedErrorCategory.XML_PARSING
        };
        return mapping[type] || UnifiedErrorCategory.UNKNOWN;
    }

    /**
     * Maps S4TK categories to unified categories
     */
    private static mapS4TKCategoryToUnified(s4tkCategory: string): UnifiedErrorCategory {
        const mapping = {
            'package_loading': UnifiedErrorCategory.PACKAGE_LOADING,
            'resource_parsing': UnifiedErrorCategory.RESOURCE_PARSING,
            'compression': UnifiedErrorCategory.COMPRESSION,
            'validation': UnifiedErrorCategory.FILE_VALIDATION,
            'hashing': UnifiedErrorCategory.HASHING,
            'xml_parsing': UnifiedErrorCategory.XML_PARSING,
            'image_processing': UnifiedErrorCategory.IMAGE_PROCESSING,
            'performance': UnifiedErrorCategory.PERFORMANCE
        };
        return mapping[s4tkCategory] || UnifiedErrorCategory.UNKNOWN;
    }
}
