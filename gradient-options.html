<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> Gradient Options</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #000;
            color: white;
        }
        
        .gradient-option {
            width: 100%;
            height: 200px;
            margin-bottom: 20px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: 600;
            position: relative;
        }
        
        .label {
            background: rgba(0,0,0,0.7);
            padding: 8px 16px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }
        
        /* Current - Subtle Dark with Warm Hints */
        .option1 {
            background: linear-gradient(135deg,
                #1a1a1a 0%,
                color-mix(in srgb, #1a1a1a 85%, #BF3100) 30%,
                color-mix(in srgb, #1a1a1a 75%, #D76A03) 60%,
                color-mix(in srgb, #1a1a1a 70%, #EC9F05) 100%);
        }
        
        /* Option 2 - Radial Burst */
        .option2 {
            background: radial-gradient(ellipse at center,
                color-mix(in srgb, #1a1a1a 60%, #BBDB06) 0%,
                color-mix(in srgb, #1a1a1a 80%, #F5BB00) 30%,
                color-mix(in srgb, #1a1a1a 85%, #EC9F05) 60%,
                #1a1a1a 100%);
        }
        
        /* Option 3 - Diagonal Sweep */
        .option3 {
            background: linear-gradient(45deg,
                #1a1a1a 0%,
                color-mix(in srgb, #1a1a1a 70%, #BBDB06) 20%,
                color-mix(in srgb, #1a1a1a 80%, #F5BB00) 40%,
                color-mix(in srgb, #1a1a1a 85%, #D76A03) 70%,
                #1a1a1a 100%);
        }
        
        /* Option 4 - Conic Gradient */
        .option4 {
            background: conic-gradient(from 45deg at 30% 70%,
                #1a1a1a 0deg,
                color-mix(in srgb, #1a1a1a 75%, #BF3100) 90deg,
                color-mix(in srgb, #1a1a1a 70%, #EC9F05) 180deg,
                color-mix(in srgb, #1a1a1a 80%, #F5BB00) 270deg,
                #1a1a1a 360deg);
        }
        
        /* Option 5 - Vertical Bands */
        .option5 {
            background: linear-gradient(180deg,
                #1a1a1a 0%,
                color-mix(in srgb, #1a1a1a 85%, #BBDB06) 25%,
                color-mix(in srgb, #1a1a1a 80%, #F5BB00) 50%,
                color-mix(in srgb, #1a1a1a 85%, #D76A03) 75%,
                #1a1a1a 100%);
        }
        
        /* Option 6 - Mesh Gradient Effect */
        .option6 {
            background: 
                radial-gradient(circle at 20% 20%, color-mix(in srgb, #1a1a1a 70%, #BBDB06) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, color-mix(in srgb, #1a1a1a 70%, #BF3100) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, color-mix(in srgb, #1a1a1a 75%, #F5BB00) 0%, transparent 50%),
                #1a1a1a;
        }
    </style>
</head>
<body>
    <h1>Simonitor Background Gradient Options</h1>
    
    <div class="gradient-option option1">
        <div class="label">Current: Subtle Dark with Warm Hints</div>
    </div>
    
    <div class="gradient-option option2">
        <div class="label">Option 2: Radial Burst from Center</div>
    </div>
    
    <div class="gradient-option option3">
        <div class="label">Option 3: Diagonal Sweep</div>
    </div>
    
    <div class="gradient-option option4">
        <div class="label">Option 4: Conic Gradient</div>
    </div>
    
    <div class="gradient-option option5">
        <div class="label">Option 5: Vertical Bands</div>
    </div>
    
    <div class="gradient-option option6">
        <div class="label">Option 6: Mesh Gradient Effect</div>
    </div>
</body>
</html>
