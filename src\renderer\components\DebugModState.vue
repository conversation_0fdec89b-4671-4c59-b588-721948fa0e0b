<template>
  <div class="debug-mod-state">
    <h3>🐛 Debug: Mod State</h3>
    
    <div class="debug-section">
      <h4>Mods Array:</h4>
      <p>Length: {{ mods?.length || 0 }}</p>
      <p>Type: {{ typeof mods }}</p>
      <p>Is Array: {{ Array.isArray(mods) }}</p>
    </div>
    
    <div class="debug-section" v-if="mods && mods.length > 0">
      <h4>First Mod Sample:</h4>
      <pre>{{ JSON.stringify(firstMod, null, 2) }}</pre>
    </div>
    
    <div class="debug-section">
      <h4>Thumbnail State:</h4>
      <p>Is Extracting: {{ isExtractingThumbnails }}</p>
      <p>Progress: {{ thumbnailProgress }}%</p>
      <p>Thumbnails Count: {{ modThumbnails?.length || 0 }}</p>
    </div>
    
    <div class="debug-section">
      <h4>Actions:</h4>
      <button @click="manualExtractThumbnails" :disabled="isExtractingThumbnails">
        Manual Extract Thumbnails
      </button>
      <button @click="logModsToConsole">
        Log Mods to Console
      </button>
    </div>
    
    <div class="debug-section" v-if="mods && mods.length > 0">
      <h4>Mods with Thumbnails:</h4>
      <div v-for="(mod, index) in mods.slice(0, 5)" :key="index" class="mod-debug-item">
        <p><strong>{{ mod.fileName }}</strong></p>
        <p>Has filePath: {{ !!mod.filePath }}</p>
        <p>Has thumbnailUrl: {{ !!mod.thumbnailUrl }}</p>
        <p>Thumbnail length: {{ mod.thumbnailUrl?.length || 0 }}</p>
        <p>Thumbnail preview: {{ mod.thumbnailUrl?.substring(0, 50) }}...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  mods?: any[];
  isExtractingThumbnails?: boolean;
  thumbnailProgress?: number;
  modThumbnails?: any[];
}>();

const emit = defineEmits<{
  extractThumbnails: [];
}>();

const firstMod = computed(() => {
  if (!props.mods || props.mods.length === 0) return null;
  
  const mod = props.mods[0];
  return {
    fileName: mod.fileName,
    filePath: mod.filePath,
    fileSize: mod.fileSize,
    author: mod.author,
    category: mod.category,
    thumbnailUrl: mod.thumbnailUrl ? `${mod.thumbnailUrl.substring(0, 50)}...` : null,
    thumbnailData: mod.thumbnailData ? `${mod.thumbnailData.substring(0, 50)}...` : null,
    hasFilePath: !!mod.filePath,
    hasThumbnailUrl: !!mod.thumbnailUrl
  };
});

const manualExtractThumbnails = () => {
  console.log('🔧 [Debug] Manual thumbnail extraction triggered');
  emit('extractThumbnails');
};

const logModsToConsole = () => {
  console.log('🔧 [Debug] Current mods state:', props.mods);
  console.log('🔧 [Debug] First 3 mods:', props.mods?.slice(0, 3));
};
</script>

<style scoped>
.debug-mod-state {
  position: fixed;
  top: 10px;
  right: 10px;
  width: 400px;
  max-height: 80vh;
  overflow-y: auto;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 16px;
  border-radius: 8px;
  font-family: monospace;
  font-size: 12px;
  z-index: 9999;
  border: 2px solid #00ff41;
}

.debug-section {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #333;
}

.debug-section h4 {
  margin: 0 0 8px 0;
  color: #00ff41;
}

.debug-section p {
  margin: 4px 0;
}

.debug-section pre {
  background: #111;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 200px;
}

.debug-section button {
  background: #00ff41;
  color: black;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 8px;
  margin-bottom: 8px;
}

.debug-section button:disabled {
  background: #666;
  cursor: not-allowed;
}

.mod-debug-item {
  background: #222;
  padding: 8px;
  margin: 4px 0;
  border-radius: 4px;
  border-left: 3px solid #00ff41;
}

.mod-debug-item p {
  margin: 2px 0;
}
</style>
