<template>
  <div class="visual-category-browser">
    <!-- Header with Search and View Controls -->
    <div class="browser-header">
      <div class="search-section">
        <div class="search-input-container">
          <MagnifyingGlassIcon class="search-icon" />
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search mods by name, category, or content..."
            class="search-input"
            @input="handleSearch"
          />
          <button v-if="searchQuery" @click="clearSearch" class="clear-search">
            <XMarkIcon class="w-4 h-4" />
          </button>
        </div>
      </div>
      
      <div class="view-controls">
        <div class="view-mode-toggle">
          <button
            :class="['view-mode-btn', { active: viewMode === 'grid' }]"
            @click="viewMode = 'grid'"
            title="Grid View"
          >
            <Squares2X2Icon class="w-5 h-5" />
          </button>
          <button
            :class="['view-mode-btn', { active: viewMode === 'list' }]"
            @click="viewMode = 'list'"
            title="List View"
          >
            <ListBulletIcon class="w-5 h-5" />
          </button>
        </div>
        
        <div class="sort-controls">
          <select v-model="sortBy" class="sort-select">
            <option value="name">Name</option>
            <option value="category">Category</option>
            <option value="size">File Size</option>
            <option value="date">Date Added</option>
          </select>
          <button
            :class="['sort-order-btn', { desc: sortOrder === 'desc' }]"
            @click="toggleSortOrder"
            title="Sort Order"
          >
            <ChevronUpIcon v-if="sortOrder === 'asc'" class="w-4 h-4" />
            <ChevronDownIcon v-else class="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="browser-content">
      <!-- Sidebar Navigation -->
      <div class="category-sidebar">
        <div class="sidebar-header">
          <h3 class="sidebar-title">Categories</h3>
          <button @click="toggleSidebar" class="sidebar-toggle">
            <ChevronLeftIcon v-if="sidebarExpanded" class="w-4 h-4" />
            <ChevronRightIcon v-else class="w-4 h-4" />
          </button>
        </div>
        
        <div v-if="sidebarExpanded" class="category-list">
          <div
            v-for="category in categories"
            :key="category.id"
            :class="['category-item', { active: selectedCategory === category.id }]"
            @click="selectCategory(category.id)"
          >
            <div class="category-icon">
              <component :is="category.icon" class="w-5 h-5" />
            </div>
            <div class="category-info">
              <span class="category-name">{{ category.name }}</span>
              <span class="category-count">{{ category.count }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Thumbnail Grid/List -->
      <div class="mod-display-area">
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner"></div>
          <p>Loading thumbnails...</p>
        </div>
        
        <div v-else-if="filteredMods.length === 0" class="empty-state">
          <FolderOpenIcon class="empty-icon" />
          <h3>No mods found</h3>
          <p>Try adjusting your search or category filters</p>
        </div>
        
        <div v-else :class="['mod-grid', viewMode]">
          <ModThumbnailCard
            v-for="mod in paginatedMods"
            :key="mod.id"
            :mod="mod"
            :view-mode="viewMode"
            @click="selectMod(mod)"
            @thumbnail-error="handleThumbnailError"
          />
        </div>
        
        <!-- Pagination -->
        <div v-if="totalPages > 1" class="pagination">
          <button
            :disabled="currentPage === 1"
            @click="goToPage(currentPage - 1)"
            class="pagination-btn"
          >
            <ChevronLeftIcon class="w-4 h-4" />
            Previous
          </button>
          
          <div class="page-numbers">
            <button
              v-for="page in visiblePages"
              :key="page"
              :class="['page-btn', { active: page === currentPage }]"
              @click="goToPage(page)"
            >
              {{ page }}
            </button>
          </div>
          
          <button
            :disabled="currentPage === totalPages"
            @click="goToPage(currentPage + 1)"
            class="pagination-btn"
          >
            Next
            <ChevronRightIcon class="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>

    <!-- Mod Detail Modal -->
    <ModDetailModal
      v-if="selectedMod"
      :mod="selectedMod"
      @close="selectedMod = null"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  Squares2X2Icon,
  ListBulletIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  FolderOpenIcon,
  SparklesIcon,
  HomeIcon,
  UserIcon,
  CogIcon,
  PuzzlePieceIcon
} from '@heroicons/vue/24/outline';

import ModThumbnailCard from './ModThumbnailCard.vue';
import ModDetailModal from './ModDetailModal.vue';
import type { ModData } from '../../../types/ModData';

// Props
interface Props {
  mods: ModData[];
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

// Reactive state
const searchQuery = ref('');
const viewMode = ref<'grid' | 'list'>('grid');
const sortBy = ref('name');
const sortOrder = ref<'asc' | 'desc'>('asc');
const selectedCategory = ref('all');
const sidebarExpanded = ref(true);
const selectedMod = ref<ModData | null>(null);
const currentPage = ref(1);
const itemsPerPage = ref(24);

// Categories configuration
const categories = ref([
  { id: 'all', name: 'All Mods', icon: HomeIcon, count: 0 },
  { id: 'cas', name: 'Create-a-Sim', icon: UserIcon, count: 0 },
  { id: 'build-buy', name: 'Build/Buy', icon: HomeIcon, count: 0 },
  { id: 'gameplay', name: 'Gameplay', icon: PuzzlePieceIcon, count: 0 },
  { id: 'scripts', name: 'Script Mods', icon: CogIcon, count: 0 },
  { id: 'other', name: 'Other', icon: SparklesIcon, count: 0 }
]);

// Computed properties
const filteredMods = computed(() => {
  let filtered = props.mods;

  // Apply category filter
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(mod => {
      const category = getCategoryForMod(mod);
      return category === selectedCategory.value;
    });
  }

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(mod =>
      mod.fileName.toLowerCase().includes(query) ||
      mod.category?.toLowerCase().includes(query) ||
      mod.description?.toLowerCase().includes(query)
    );
  }

  // Apply sorting
  filtered.sort((a, b) => {
    let aValue: any, bValue: any;
    
    switch (sortBy.value) {
      case 'name':
        aValue = a.fileName.toLowerCase();
        bValue = b.fileName.toLowerCase();
        break;
      case 'category':
        aValue = a.category || '';
        bValue = b.category || '';
        break;
      case 'size':
        aValue = a.fileSize || 0;
        bValue = b.fileSize || 0;
        break;
      case 'date':
        aValue = a.lastModified || 0;
        bValue = b.lastModified || 0;
        break;
      default:
        return 0;
    }

    if (aValue < bValue) return sortOrder.value === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder.value === 'asc' ? 1 : -1;
    return 0;
  });

  return filtered;
});

const paginatedMods = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredMods.value.slice(start, end);
});

const totalPages = computed(() => {
  return Math.ceil(filteredMods.value.length / itemsPerPage.value);
});

const visiblePages = computed(() => {
  const pages = [];
  const total = totalPages.value;
  const current = currentPage.value;
  
  // Show up to 7 pages around current page
  let start = Math.max(1, current - 3);
  let end = Math.min(total, current + 3);
  
  // Adjust if we're near the beginning or end
  if (end - start < 6) {
    if (start === 1) {
      end = Math.min(total, start + 6);
    } else {
      start = Math.max(1, end - 6);
    }
  }
  
  for (let i = start; i <= end; i++) {
    pages.push(i);
  }
  
  return pages;
});

// Methods
const handleSearch = () => {
  currentPage.value = 1; // Reset to first page when searching
};

const clearSearch = () => {
  searchQuery.value = '';
  currentPage.value = 1;
};

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
};

const selectCategory = (categoryId: string) => {
  selectedCategory.value = categoryId;
  currentPage.value = 1;
};

const toggleSidebar = () => {
  sidebarExpanded.value = !sidebarExpanded.value;
};

const selectMod = (mod: ModData) => {
  selectedMod.value = mod;
};

const goToPage = (page: number) => {
  currentPage.value = page;
  // Scroll to top of mod grid
  const modGrid = document.querySelector('.mod-display-area');
  if (modGrid) {
    modGrid.scrollTop = 0;
  }
};

const handleThumbnailError = (modId: string) => {
  console.warn(`Thumbnail failed to load for mod: ${modId}`);
  // Could implement fallback thumbnail logic here
};

const getCategoryForMod = (mod: ModData): string => {
  // Map mod categories to our category system
  const category = mod.category?.toLowerCase() || '';
  
  if (category.includes('cas') || category.includes('create-a-sim')) return 'cas';
  if (category.includes('build') || category.includes('buy') || category.includes('object')) return 'build-buy';
  if (category.includes('script') || category.includes('python')) return 'scripts';
  if (category.includes('gameplay') || category.includes('trait') || category.includes('career')) return 'gameplay';
  
  return 'other';
};

const updateCategoryCounts = () => {
  categories.value.forEach(category => {
    if (category.id === 'all') {
      category.count = props.mods.length;
    } else {
      category.count = props.mods.filter(mod => getCategoryForMod(mod) === category.id).length;
    }
  });
};

// Watchers
watch(() => props.mods, updateCategoryCounts, { immediate: true });

// Lifecycle
onMounted(() => {
  updateCategoryCounts();
});
</script>

<style scoped>
.visual-category-browser {
  @apply flex flex-col h-full bg-gray-50 dark:bg-gray-900;
}

/* Header Styles */
.browser-header {
  @apply flex items-center justify-between p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700;
}

.search-section {
  @apply flex-1 max-w-md;
}

.search-input-container {
  @apply relative;
}

.search-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400;
}

.search-input {
  @apply w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg 
         bg-white dark:bg-gray-700 text-gray-900 dark:text-white
         focus:ring-2 focus:ring-green-500 focus:border-transparent
         placeholder-gray-500 dark:placeholder-gray-400;
}

.clear-search {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300;
}

.view-controls {
  @apply flex items-center space-x-4;
}

.view-mode-toggle {
  @apply flex rounded-lg border border-gray-300 dark:border-gray-600 overflow-hidden;
}

.view-mode-btn {
  @apply px-3 py-2 bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-300 
         hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors;
}

.view-mode-btn.active {
  @apply bg-green-500 text-white;
}

.sort-controls {
  @apply flex items-center space-x-2;
}

.sort-select {
  @apply px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
         bg-white dark:bg-gray-700 text-gray-900 dark:text-white
         focus:ring-2 focus:ring-green-500 focus:border-transparent;
}

.sort-order-btn {
  @apply p-2 border border-gray-300 dark:border-gray-600 rounded-lg
         bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-300
         hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors;
}

/* Content Area */
.browser-content {
  @apply flex flex-1 overflow-hidden;
}

/* Sidebar Styles */
.category-sidebar {
  @apply bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 
         transition-all duration-300 ease-in-out;
  width: 280px;
}

.sidebar-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700;
}

.sidebar-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.sidebar-toggle {
  @apply p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200;
}

.category-list {
  @apply p-2 space-y-1;
}

.category-item {
  @apply flex items-center p-3 rounded-lg cursor-pointer
         hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
}

.category-item.active {
  @apply bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200;
}

.category-icon {
  @apply mr-3 text-gray-500 dark:text-gray-400;
}

.category-item.active .category-icon {
  @apply text-green-600 dark:text-green-400;
}

.category-info {
  @apply flex-1 flex items-center justify-between;
}

.category-name {
  @apply font-medium;
}

.category-count {
  @apply text-sm text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-600 
         px-2 py-1 rounded-full;
}

.category-item.active .category-count {
  @apply bg-green-200 dark:bg-green-800 text-green-800 dark:text-green-200;
}

/* Main Display Area */
.mod-display-area {
  @apply flex-1 flex flex-col overflow-hidden;
}

.loading-state, .empty-state {
  @apply flex-1 flex flex-col items-center justify-center text-gray-500 dark:text-gray-400;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-gray-300 border-t-green-500 rounded-full animate-spin mb-4;
}

.empty-icon {
  @apply w-16 h-16 mb-4;
}

.mod-grid {
  @apply flex-1 overflow-auto p-4;
}

.mod-grid.grid {
  @apply grid gap-4;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.mod-grid.list {
  @apply space-y-2;
}

/* Pagination */
.pagination {
  @apply flex items-center justify-center space-x-2 p-4 border-t border-gray-200 dark:border-gray-700;
}

.pagination-btn {
  @apply flex items-center space-x-1 px-3 py-2 border border-gray-300 dark:border-gray-600 
         rounded-lg bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300
         hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed;
}

.page-numbers {
  @apply flex space-x-1;
}

.page-btn {
  @apply w-10 h-10 flex items-center justify-center border border-gray-300 dark:border-gray-600
         rounded-lg bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300
         hover:bg-gray-50 dark:hover:bg-gray-600;
}

.page-btn.active {
  @apply bg-green-500 border-green-500 text-white;
}
</style>
