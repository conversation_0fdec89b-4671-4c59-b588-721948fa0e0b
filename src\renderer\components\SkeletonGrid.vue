<template>
  <div 
    class="skeleton-grid"
    :class="[
      `skeleton-grid--${size}`,
      { 'skeleton-grid--staggered': staggered }
    ]"
    role="presentation"
    aria-hidden="true"
    :aria-label="`Loading ${count} items`"
  >
    <ModCardSkeleton
      v-for="index in displayCount"
      :key="`skeleton-${index}`"
      :size="size"
      :animation="getAnimationForIndex(index)"
      :style="getSkeletonStyle(index)"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import ModCardSkeleton from './ModCardSkeleton.vue';

interface Props {
  /** Number of skeleton items to show */
  count?: number;
  /** Size variant matching the mod card sizes */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'small' | 'medium' | 'large';
  /** Animation type for skeleton elements */
  animation?: 'pulse' | 'wave' | 'shimmer' | 'none';
  /** Whether to stagger the animation timing */
  staggered?: boolean;
  /** Maximum number of skeletons to show (for performance) */
  maxCount?: number;
}

const props = withDefaults(defineProps<Props>(), {
  count: 12,
  size: 'sm',
  animation: 'shimmer',
  staggered: true,
  maxCount: 24,
});

const displayCount = computed(() => {
  return Math.min(props.count, props.maxCount);
});

const getAnimationForIndex = (index: number): string => {
  // For staggered animations, vary the animation type slightly
  if (props.staggered && props.animation !== 'none') {
    const animations = ['shimmer', 'pulse', 'wave'];
    return animations[(index - 1) % animations.length];
  }
  return props.animation;
};

const getSkeletonStyle = (index: number) => {
  if (!props.staggered) return {};
  
  // Stagger the animation delay for a more natural loading effect
  const delay = ((index - 1) * 100) % 1000; // Cycle every 10 items
  return {
    animationDelay: `${delay}ms`,
  };
};
</script>

<style scoped>
/* ===== SKELETON GRID BASE ===== */
.skeleton-grid {
  display: grid;
  gap: var(--space-6);
  width: 100%;
  align-items: start;
}

/* ===== SIZE-BASED GRID LAYOUTS ===== */
/* New enhanced size system */
.skeleton-grid--xs {
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: var(--space-3);
}

.skeleton-grid--sm {
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: var(--space-4);
}

.skeleton-grid--md {
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: var(--space-5);
}

.skeleton-grid--lg {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-6);
}

.skeleton-grid--xl {
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  gap: var(--space-8);
}

/* Legacy size support for backward compatibility */
.skeleton-grid--small {
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: var(--space-4);
}

.skeleton-grid--medium {
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: var(--space-5);
}

.skeleton-grid--large {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-6);
}

/* ===== STAGGERED ANIMATION SUPPORT ===== */
.skeleton-grid--staggered .mod-card-skeleton {
  opacity: 0;
  animation: skeleton-fade-in 0.6s ease-out forwards;
}

@keyframes skeleton-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .skeleton-grid--xl {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--space-6);
  }

  .skeleton-grid--lg {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--space-5);
  }

  /* Legacy support */
  .skeleton-grid--large {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--space-5);
  }
}

@media (max-width: 768px) {
  .skeleton-grid--xl {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: var(--space-5);
  }

  .skeleton-grid--lg {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: var(--space-4);
  }

  .skeleton-grid--md {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: var(--space-4);
  }

  .skeleton-grid--sm {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--space-3);
  }

  .skeleton-grid--xs {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: var(--space-3);
  }

  /* Legacy support */
  .skeleton-grid--medium,
  .skeleton-grid--large {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: var(--space-4);
  }

  .skeleton-grid--small {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--space-3);
  }
}

@media (max-width: 640px) {
  .skeleton-grid--xs,
  .skeleton-grid--sm,
  .skeleton-grid--md,
  .skeleton-grid--lg,
  .skeleton-grid--xl {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: var(--space-3);
  }

  /* Legacy support */
  .skeleton-grid--small,
  .skeleton-grid--medium,
  .skeleton-grid--large {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: var(--space-3);
  }
}

@media (max-width: 360px) {
  .skeleton-grid--xs,
  .skeleton-grid--sm,
  .skeleton-grid--md,
  .skeleton-grid--lg,
  .skeleton-grid--xl,
  .skeleton-grid--small,
  .skeleton-grid--medium,
  .skeleton-grid--large {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: var(--space-2);
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .skeleton-grid--staggered .mod-card-skeleton {
    animation: none;
    opacity: 0.7;
  }
  
  @keyframes skeleton-fade-in {
    from, to {
      opacity: 0.7;
      transform: none;
    }
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.skeleton-grid {
  /* Use GPU acceleration for better performance */
  transform: translateZ(0);
  will-change: contents;
}

/* Limit the number of concurrent animations for performance */
.skeleton-grid--staggered .mod-card-skeleton:nth-child(n+13) {
  animation-delay: 0ms;
}
</style>
