<template>
  <Teleport to="body">
    <div
      v-if="isOpen"
      class="image-preview-overlay"
      @click="handleOverlayClick"
      @keydown.esc="closePreview"
      tabindex="0"
    >
      <!-- Modal Container -->
      <div class="image-preview-modal" @click.stop>
        <!-- Header -->
        <div class="preview-header">
          <div class="preview-title">
            <h3>{{ getDisplayName(currentThumbnail?.modFileName || '') }}</h3>
            <p class="preview-subtitle">
              {{ formatCategory(currentThumbnail?.category || '') }}
              <span v-if="currentThumbnail?.subcategory">• {{ currentThumbnail.subcategory }}</span>
            </p>
          </div>
          
          <div class="preview-actions">
            <!-- Navigation -->
            <button
              v-if="hasPrevious"
              @click="navigatePrevious"
              class="nav-btn"
              title="Previous image"
            >
              <ChevronLeftIcon class="w-5 h-5" />
            </button>
            
            <span v-if="thumbnails.length > 1" class="nav-counter">
              {{ currentIndex + 1 }} / {{ thumbnails.length }}
            </span>
            
            <button
              v-if="hasNext"
              @click="navigateNext"
              class="nav-btn"
              title="Next image"
            >
              <ChevronRightIcon class="w-5 h-5" />
            </button>
            
            <!-- Zoom Controls -->
            <div class="zoom-controls">
              <button
                @click="zoomOut"
                :disabled="zoomLevel <= minZoom"
                class="zoom-btn"
                title="Zoom out"
              >
                <MinusIcon class="w-4 h-4" />
              </button>
              
              <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
              
              <button
                @click="zoomIn"
                :disabled="zoomLevel >= maxZoom"
                class="zoom-btn"
                title="Zoom in"
              >
                <PlusIcon class="w-4 h-4" />
              </button>
              
              <button
                @click="resetZoom"
                class="zoom-btn"
                title="Reset zoom"
              >
                <ArrowsPointingOutIcon class="w-4 h-4" />
              </button>
            </div>
            
            <!-- Close Button -->
            <button
              @click="closePreview"
              class="close-btn"
              title="Close preview"
            >
              <XMarkIcon class="w-5 h-5" />
            </button>
          </div>
        </div>
        
        <!-- Image Container -->
        <div class="preview-content" ref="contentRef">
          <div
            class="image-container"
            :style="imageContainerStyle"
            @wheel="handleWheel"
            @mousedown="startPan"
            @mousemove="handlePan"
            @mouseup="endPan"
            @mouseleave="endPan"
          >
            <img
              v-if="currentThumbnail"
              :src="currentThumbnail.imageData"
              :alt="currentThumbnail.modFileName"
              class="preview-image"
              :style="imageStyle"
              @load="onImageLoad"
              @error="onImageError"
              draggable="false"
            />
            
            <!-- Loading State -->
            <div v-if="isImageLoading" class="image-loading">
              <div class="loading-spinner"></div>
              <p>Loading image...</p>
            </div>
            
            <!-- Error State -->
            <div v-if="imageError" class="image-error">
              <ExclamationTriangleIcon class="w-12 h-12 text-error mb-4" />
              <p>Failed to load image</p>
              <button @click="retryLoad" class="retry-btn">
                Retry
              </button>
            </div>
          </div>
        </div>
        
        <!-- Footer with Image Info -->
        <div class="preview-footer">
          <div class="image-info">
            <div class="info-group">
              <span class="info-label">Dimensions:</span>
              <span class="info-value">{{ currentThumbnail?.width }}×{{ currentThumbnail?.height }}</span>
            </div>
            
            <div class="info-group">
              <span class="info-label">Format:</span>
              <span class="info-value">{{ currentThumbnail?.format.toUpperCase() }}</span>
            </div>
            
            <div class="info-group">
              <span class="info-label">Size:</span>
              <span class="info-value">{{ formatFileSize(currentThumbnail?.fileSize || 0) }}</span>
            </div>
            
            <div class="info-group">
              <span class="info-label">Quality:</span>
              <span class="info-value">
                {{ currentThumbnail?.isHighQuality ? 'High' : 'Standard' }}
                <SparklesIcon v-if="currentThumbnail?.isHighQuality" class="w-4 h-4 inline ml-1 text-success" />
              </span>
            </div>
            
            <div v-if="currentThumbnail?.isFallback" class="info-group">
              <span class="info-label">Type:</span>
              <span class="info-value text-warning">Fallback Icon</span>
            </div>
          </div>
          
          <div class="footer-actions">
            <button
              @click="downloadImage"
              class="action-btn"
              title="Download image"
            >
              <ArrowDownTrayIcon class="w-4 h-4" />
              Download
            </button>
            
            <button
              @click="copyImageData"
              class="action-btn"
              title="Copy image data"
            >
              <ClipboardIcon class="w-4 h-4" />
              Copy
            </button>
            
            <button
              @click="showModDetails"
              class="action-btn"
              title="Show mod details"
            >
              <InformationCircleIcon class="w-4 h-4" />
              Details
            </button>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';
import {
  XMarkIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  PlusIcon,
  MinusIcon,
  ArrowsPointingOutIcon,
  ArrowDownTrayIcon,
  ClipboardIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon,
  SparklesIcon
} from '@heroicons/vue/24/outline';

import type { ThumbnailData } from '../../services/visual/ThumbnailExtractionService';

// Props
interface Props {
  isOpen: boolean;
  thumbnails: ThumbnailData[];
  initialIndex?: number;
}

const props = withDefaults(defineProps<Props>(), {
  initialIndex: 0
});

// Emits
const emit = defineEmits<{
  close: [];
  navigate: [index: number];
  showModDetails: [thumbnail: ThumbnailData];
}>();

// Reactive state
const currentIndex = ref(props.initialIndex);
const zoomLevel = ref(1);
const panX = ref(0);
const panY = ref(0);
const isPanning = ref(false);
const lastPanX = ref(0);
const lastPanY = ref(0);
const isImageLoading = ref(false);
const imageError = ref(false);
const contentRef = ref<HTMLElement>();

// Constants
const minZoom = 0.1;
const maxZoom = 5;
const zoomStep = 0.2;

// Computed properties
const currentThumbnail = computed(() => {
  return props.thumbnails[currentIndex.value] || null;
});

const hasPrevious = computed(() => {
  return currentIndex.value > 0;
});

const hasNext = computed(() => {
  return currentIndex.value < props.thumbnails.length - 1;
});

const imageContainerStyle = computed(() => ({
  cursor: isPanning.value ? 'grabbing' : (zoomLevel.value > 1 ? 'grab' : 'default')
}));

const imageStyle = computed(() => ({
  transform: `scale(${zoomLevel.value}) translate(${panX.value}px, ${panY.value}px)`,
  transformOrigin: 'center center',
  transition: isPanning.value ? 'none' : 'transform 0.2s ease'
}));

// Methods
const closePreview = () => {
  emit('close');
  resetView();
};

const navigatePrevious = () => {
  if (hasPrevious.value) {
    currentIndex.value--;
    resetView();
    emit('navigate', currentIndex.value);
  }
};

const navigateNext = () => {
  if (hasNext.value) {
    currentIndex.value++;
    resetView();
    emit('navigate', currentIndex.value);
  }
};

const zoomIn = () => {
  if (zoomLevel.value < maxZoom) {
    zoomLevel.value = Math.min(maxZoom, zoomLevel.value + zoomStep);
  }
};

const zoomOut = () => {
  if (zoomLevel.value > minZoom) {
    zoomLevel.value = Math.max(minZoom, zoomLevel.value - zoomStep);
    
    // Reset pan if zoomed out too much
    if (zoomLevel.value <= 1) {
      panX.value = 0;
      panY.value = 0;
    }
  }
};

const resetZoom = () => {
  zoomLevel.value = 1;
  panX.value = 0;
  panY.value = 0;
};

const resetView = () => {
  resetZoom();
  isImageLoading.value = true;
  imageError.value = false;
};

const handleWheel = (event: WheelEvent) => {
  event.preventDefault();
  
  if (event.deltaY < 0) {
    zoomIn();
  } else {
    zoomOut();
  }
};

const startPan = (event: MouseEvent) => {
  if (zoomLevel.value > 1) {
    isPanning.value = true;
    lastPanX.value = event.clientX;
    lastPanY.value = event.clientY;
  }
};

const handlePan = (event: MouseEvent) => {
  if (isPanning.value && zoomLevel.value > 1) {
    const deltaX = event.clientX - lastPanX.value;
    const deltaY = event.clientY - lastPanY.value;
    
    panX.value += deltaX / zoomLevel.value;
    panY.value += deltaY / zoomLevel.value;
    
    lastPanX.value = event.clientX;
    lastPanY.value = event.clientY;
  }
};

const endPan = () => {
  isPanning.value = false;
};

const handleOverlayClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    closePreview();
  }
};

const onImageLoad = () => {
  isImageLoading.value = false;
  imageError.value = false;
};

const onImageError = () => {
  isImageLoading.value = false;
  imageError.value = true;
};

const retryLoad = () => {
  imageError.value = false;
  isImageLoading.value = true;
  
  // Force image reload by updating src
  nextTick(() => {
    const img = document.querySelector('.preview-image') as HTMLImageElement;
    if (img && currentThumbnail.value) {
      img.src = currentThumbnail.value.imageData;
    }
  });
};

const downloadImage = () => {
  if (!currentThumbnail.value) return;
  
  const link = document.createElement('a');
  link.href = currentThumbnail.value.imageData;
  link.download = `${currentThumbnail.value.modFileName}_thumbnail.${currentThumbnail.value.format}`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const copyImageData = async () => {
  if (!currentThumbnail.value) return;
  
  try {
    await navigator.clipboard.writeText(currentThumbnail.value.imageData);
    // Could show a toast notification here
  } catch (error) {
    console.error('Failed to copy image data:', error);
  }
};

const showModDetails = () => {
  if (currentThumbnail.value) {
    emit('showModDetails', currentThumbnail.value);
  }
};

const getDisplayName = (fileName: string): string => {
  return fileName
    .replace(/\.(package|ts4script)$/i, '')
    .replace(/[_-]/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
};

const formatCategory = (category: string): string => {
  return category
    .replace(/[_-]/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// Keyboard navigation
const handleKeydown = (event: KeyboardEvent) => {
  if (!props.isOpen) return;
  
  switch (event.key) {
    case 'ArrowLeft':
      event.preventDefault();
      navigatePrevious();
      break;
    case 'ArrowRight':
      event.preventDefault();
      navigateNext();
      break;
    case '+':
    case '=':
      event.preventDefault();
      zoomIn();
      break;
    case '-':
      event.preventDefault();
      zoomOut();
      break;
    case '0':
      event.preventDefault();
      resetZoom();
      break;
  }
};

// Watchers
watch(() => props.initialIndex, (newIndex) => {
  currentIndex.value = newIndex;
  resetView();
});

watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    resetView();
    nextTick(() => {
      const overlay = document.querySelector('.image-preview-overlay') as HTMLElement;
      overlay?.focus();
    });
  }
});

// Lifecycle
onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});
</script>

<style scoped>
.image-preview-overlay {
  @apply fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4;
  animation: fadeIn 0.2s ease-out;
}

.image-preview-modal {
  @apply bg-surface rounded-lg shadow-2xl max-w-6xl max-h-full w-full flex flex-col overflow-hidden;
  animation: slideIn 0.3s ease-out;
}

.preview-header {
  @apply flex items-center justify-between p-4 border-b bg-background;
}

.preview-title h3 {
  @apply font-semibold text-lg;
}

.preview-subtitle {
  @apply text-sm text-muted;
}

.preview-actions {
  @apply flex items-center gap-2;
}

.nav-btn,
.zoom-btn,
.close-btn {
  @apply p-2 rounded hover:bg-surface transition-colors;
}

.nav-counter {
  @apply text-sm text-muted px-2;
}

.zoom-controls {
  @apply flex items-center gap-1 border rounded p-1;
}

.zoom-level {
  @apply text-xs px-2 min-w-12 text-center;
}

.preview-content {
  @apply flex-1 overflow-hidden relative bg-background;
}

.image-container {
  @apply w-full h-full flex items-center justify-center overflow-hidden;
  min-height: 400px;
}

.preview-image {
  @apply max-w-full max-h-full object-contain select-none;
}

.image-loading,
.image-error {
  @apply flex flex-col items-center justify-center text-center p-8;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mb-4;
}

.retry-btn {
  @apply mt-4 px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors;
}

.preview-footer {
  @apply flex items-center justify-between p-4 border-t bg-background;
}

.image-info {
  @apply flex items-center gap-6 text-sm;
}

.info-group {
  @apply flex items-center gap-1;
}

.info-label {
  @apply text-muted;
}

.info-value {
  @apply font-medium;
}

.footer-actions {
  @apply flex items-center gap-2;
}

.action-btn {
  @apply flex items-center gap-2 px-3 py-2 text-sm border rounded hover:bg-surface transition-colors;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { 
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to { 
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .preview-header {
    @apply flex-col gap-4;
  }
  
  .preview-actions {
    @apply w-full justify-between;
  }
  
  .preview-footer {
    @apply flex-col gap-4;
  }
  
  .image-info {
    @apply grid grid-cols-2 gap-4 w-full;
  }
}
</style>
