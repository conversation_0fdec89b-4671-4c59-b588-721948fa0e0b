import type { SizeOption } from '../types/dashboard';

// Shared constants for dashboard components
export const SIZE_OPTIONS: SizeOption[] = [
  { value: 'xs', label: 'XS', minWidth: 180 },
  { value: 'sm', label: 'SM', minWidth: 220 },
  { value: 'md', label: 'MD', minWidth: 260 },
  { value: 'lg', label: 'LG', minWidth: 300 },
  { value: 'xl', label: 'XL', minWidth: 340 }
];

export const DEFAULT_THUMBNAIL_SIZE = 'sm';
export const DEFAULT_ITEMS_PER_PAGE = 24;
export const DEFAULT_CURRENT_PAGE = 1;

// File type options
export const FILE_TYPE_OPTIONS = [
  { value: '', label: 'All Types' },
  { value: '.package', label: 'Package Files' },
  { value: '.ts4script', label: 'Script Files' }
];

// Quality filter options
export const QUALITY_OPTIONS = [
  { value: '', label: 'All Quality' },
  { value: 'excellent', label: 'Excellent (90-100)' },
  { value: 'good', label: 'Good (70-89)' },
  { value: 'fair', label: 'Fair (50-69)' },
  { value: 'poor', label: 'Poor (0-49)' }
];

// Sort options
export const SORT_OPTIONS = [
  { value: 'name', label: 'Sort by Name' },
  { value: 'quality', label: 'Sort by Quality' },
  { value: 'size', label: 'Sort by Size' },
  { value: 'author', label: 'Sort by Author' }
];

// Animation delays
export const ANIMATION_DELAY_PER_ITEM = 50; // ms

// Responsive breakpoints
export const BREAKPOINTS = {
  mobile: 480,
  tablet: 768,
  desktop: 1024
} as const;