import { watch, shallowRef, type Ref } from 'vue';
import type { ModData, FileTypeFilter, QualityFilter, SortOption } from '../types/dashboard';

export function useModFiltering(
  mods: Ref<ModData[]>,
  searchQuery: Ref<string>,
  selectedFileTypeFilter: Ref<FileTypeFilter>,
  selectedQualityFilter: Ref<QualityFilter>,
  selectedSortOption: Ref<SortOption>
) {
  // Use shallowRef for filtered results to optimize performance
  const filteredMods = shallowRef<ModData[]>([]);

  // Watch for changes and update filtered mods
  watch(
    [mods, searchQuery, selectedFileTypeFilter, selectedQualityFilter, selectedSortOption],
    () => {
      if (!mods.value || !Array.isArray(mods.value)) {
        filteredMods.value = [];
        return;
      }

      let filtered = [...mods.value];

      // Search filter
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        filtered = filtered.filter(mod =>
          (mod.fileName && mod.fileName.toLowerCase().includes(query)) ||
          (mod.author && mod.author.toLowerCase().includes(query)) ||
          (mod.modName && mod.modName.toLowerCase().includes(query))
        );
      }

      // File type filter
      if (selectedFileTypeFilter.value) {
        filtered = filtered.filter(mod =>
          mod && mod.fileExtension === selectedFileTypeFilter.value
        );
      }

      // Quality filter
      if (selectedQualityFilter.value) {
        filtered = filtered.filter(mod => {
          if (!mod || typeof mod.qualityScore !== 'number') return false;
          const score = mod.qualityScore;
          switch (selectedQualityFilter.value) {
            case 'excellent': return score >= 90;
            case 'good': return score >= 70 && score < 90;
            case 'fair': return score >= 50 && score < 70;
            case 'poor': return score < 50;
            default: return true;
          }
        });
      }

      // Sort
      filtered.sort((a, b) => {
        switch (selectedSortOption.value) {
          case 'name':
            return (a.fileName || '').localeCompare(b.fileName || '');
          case 'quality':
            return (b.qualityScore || 0) - (a.qualityScore || 0);
          case 'size':
            // Assuming we have a size property, fallback to 0
            return ((b as any).size || 0) - ((a as any).size || 0);
          case 'author':
            return (a.author || '').localeCompare(b.author || '');
          default:
            return 0;
        }
      });

      filteredMods.value = filtered;
    },
    { immediate: true }
  );

  return {
    filteredMods
  };
}