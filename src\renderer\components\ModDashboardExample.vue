<template>
  <div class="mod-dashboard-example">
    <h1>Simonitor - Mod Management Tool</h1>
    
    <div class="controls">
      <button @click="startAnalysis" :disabled="isScanningFolder || isProcessingMods">
        {{ getButtonText() }}
      </button>
      <button @click="reset" v-if="!isScanningFolder && !isProcessingMods">
        Reset
      </button>
    </div>

    <!-- ModDashboard with corrected loading flow -->
    <ModDashboard 
      :mods="discoveredMods"
      :is-scanning-folder="isScanningFolder"
      :is-processing-mods="isProcessingMods"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ModDashboard from './ModDashboard.vue';

// Loading states for the two-phase system
const isScanningFolder = ref(false);
const isProcessingMods = ref(false);
const discoveredMods = ref<any[]>([]);

const getButtonText = () => {
  if (isScanningFolder.value) return 'Scanning Folder...';
  if (isProcessingMods.value) return 'Processing Mods...';
  return 'Analyze Mods Folder';
};

const startAnalysis = async () => {
  try {
    // Phase 1: Folder Scanning (no skeleton, just spinner)
    isScanningFolder.value = true;
    isProcessingMods.value = false;
    discoveredMods.value = [];
    
    // Simulate folder scanning
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Simulate discovering mods
    const mockMods = Array.from({ length: 15 }, (_, i) => ({
      id: `mod-${i}`,
      fileName: `mod-${i}.package`,
      name: `Sample Mod ${i + 1}`,
      author: `Author ${i + 1}`,
      fileSize: Math.floor(Math.random() * 1000000),
      category: ['cas', 'objects', 'script'][Math.floor(Math.random() * 3)],
    }));
    
    discoveredMods.value = mockMods;
    
    // Phase 2: Mod Processing (skeleton grid appears)
    isScanningFolder.value = false;
    isProcessingMods.value = true;
    
    // Simulate mod analysis
    await new Promise(resolve => setTimeout(resolve, 4000));
    
    // Analysis complete
    isProcessingMods.value = false;
    
  } catch (error) {
    console.error('Analysis failed:', error);
    reset();
  }
};

const reset = () => {
  isScanningFolder.value = false;
  isProcessingMods.value = false;
  discoveredMods.value = [];
};
</script>

<style scoped>
.mod-dashboard-example {
  padding: var(--space-6);
  max-width: 1400px;
  margin: 0 auto;
}

.controls {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

button {
  padding: var(--space-3) var(--space-6);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

button:hover:not(:disabled) {
  background: var(--bg-secondary);
  border-color: var(--border-strong);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

h1 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-6);
}
</style>
