<template>
  <div class="dashboard-filters">
    <div class="filters-container">
      <div class="filter-group">
        <label class="filter-label">Type:</label>
        <select
          :value="selectedFileTypeFilter"
          @change="handleFileTypeChange"
          class="filter-select"
          aria-label="Filter by file type"
        >
          <option value="">All Types</option>
          <option value=".package">Package Files</option>
          <option value=".ts4script">Script Files</option>
        </select>
      </div>

      <div class="filter-group">
        <label class="filter-label">Quality:</label>
        <select
          :value="selectedQualityFilter"
          @change="handleQualityChange"
          class="filter-select"
          aria-label="Filter by quality"
        >
          <option value="">All Quality</option>
          <option value="excellent">Excellent (90-100)</option>
          <option value="good">Good (70-89)</option>
          <option value="fair">Fair (50-69)</option>
          <option value="poor">Poor (0-49)</option>
        </select>
      </div>

      <div class="filter-group">
        <label class="filter-label">Sort:</label>
        <select
          :value="selectedSortOption"
          @change="handleSortChange"
          class="filter-select"
          aria-label="Sort options"
        >
          <option value="name">Sort by Name</option>
          <option value="quality">Sort by Quality</option>
          <option value="size">Sort by Size</option>
          <option value="author">Sort by Author</option>
        </select>
      </div>

      <div v-if="hasActiveFilters" class="filter-actions">
        <button @click="clearAllFilters" class="btn btn-error btn-sm">
          <svg class="clear-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
          Clear Filters
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type {
  FileTypeFilter,
  QualityFilter,
  SortOption,
  UpdateEvent
} from '../../types/dashboard';

interface DashboardFiltersProps {
  selectedFileTypeFilter: FileTypeFilter;
  selectedQualityFilter: QualityFilter;
  selectedSortOption: SortOption;
  searchQuery?: string;
}

interface DashboardFiltersEmits {
  'update:selectedFileTypeFilter': UpdateEvent<FileTypeFilter>;
  'update:selectedQualityFilter': UpdateEvent<QualityFilter>;
  'update:selectedSortOption': UpdateEvent<SortOption>;
  'clear-all-filters': [];
}

// Props
const props = defineProps<DashboardFiltersProps>();

// Emits
const emit = defineEmits<DashboardFiltersEmits>();

// Computed properties
const hasActiveFilters = computed(() =>
  props.searchQuery ||
  props.selectedFileTypeFilter ||
  props.selectedQualityFilter
);

// Methods
const handleFileTypeChange = (event: Event) => {
  const target = event.target as HTMLSelectElement;
  emit('update:selectedFileTypeFilter', target.value as FileTypeFilter);
};

const handleQualityChange = (event: Event) => {
  const target = event.target as HTMLSelectElement;
  emit('update:selectedQualityFilter', target.value as QualityFilter);
};

const handleSortChange = (event: Event) => {
  const target = event.target as HTMLSelectElement;
  emit('update:selectedSortOption', target.value as SortOption);
};

const clearAllFilters = () => {
  emit('clear-all-filters');
};
</script>

<style scoped>
/* Import shared design system styles */
@import '../../styles/dashboard-shared.css';
@import '../../styles/simonitor-design-system.css';

.dashboard-filters {
  margin-bottom: 16px;
}

.filters-container {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
  background: var(--bg-elevated);
  padding: 16px;
  border-radius: 12px;
  border: 1px solid var(--border-light);
  backdrop-filter: blur(10px);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 0;
}

.filter-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  min-width: fit-content;
}

.filter-select {
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: 8px;
  padding: 8px 12px;
  color: var(--text-primary);
  font-size: 0.9rem;
  font-weight: 500;
  min-width: 140px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.filter-select:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-medium);
}

.filter-select:focus {
  outline: none;
  background: var(--bg-tertiary);
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-bg);
}

.filter-select option {
  background: var(--bg-primary);
  color: var(--text-primary);
  padding: 8px;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  align-items: center;
}

/* Clear filters button now uses design system btn-error class */

.clear-icon {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .filters-container {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
  }

  .filter-label {
    font-size: 0.85rem;
  }

  .filter-select {
    min-width: 0;
    width: 100%;
  }

  .filter-actions {
    margin-left: 0;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .filters-container {
    padding: 8px;
    gap: 8px;
  }

  .filter-select {
    padding: 6px 10px;
    font-size: 0.85rem;
  }

  /* Clear filters button responsive styling handled by btn-sm class */

  .clear-icon {
    width: 12px;
    height: 12px;
  }
}
</style>