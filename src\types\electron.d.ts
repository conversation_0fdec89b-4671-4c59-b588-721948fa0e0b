/**
 * TypeScript declarations for Electron API exposed via contextBridge
 */

interface ElectronAPI {
  /**
   * Analyzes a single package file with detailed intelligence
   */
  analyzePackage: (filePath: string) => Promise<any>;

  /**
   * Analyzes all mod files in a folder recursively
   */
  analyzeModsFolder: (folderPath: string) => Promise<any>;

  /**
   * Opens a folder selection dialog
   */
  selectModsFolder: () => Promise<any>;

  /**
   * Exports analysis results to file
   */
  exportResults: (data: any, format: 'json' | 'csv') => Promise<any>;

  /**
   * Extracts thumbnails from a mod package file
   */
  extractThumbnails: (filePath: string, options?: any) => Promise<{
    success: boolean;
    thumbnails: any[];
    errors: string[];
    processingTime: number;
  }>;

  /**
   * Legacy support for old analysis method
   */
  onAnalysisResult: (callback: (result: any) => void) => void;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

export {};
