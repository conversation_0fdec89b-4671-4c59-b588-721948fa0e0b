// Shared types for dashboard components
export type ThumbnailSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type ViewMode = 'grid' | 'list';
export type FileTypeFilter = '' | '.package' | '.ts4script';
export type QualityFilter = '' | 'excellent' | 'good' | 'fair' | 'poor';
export type SortOption = 'name' | 'quality' | 'size' | 'author';

export interface SizeOption {
  value: ThumbnailSize;
  label: string;
  minWidth: number;
}

export interface ModData {
  fileName?: string;
  filePath?: string;
  author?: string;
  modName?: string;
  fileExtension?: string;
  qualityScore?: number;
  thumbnailUrl?: string;
  thumbnails?: any[];
  primaryThumbnail?: any;
  thumbnailVariations?: any[];
  hasMultipleVariations?: boolean;
}

// Common event emit types
export type UpdateEvent<T> = [value: T];
export type PageChangeEvent = [page: number];
export type ModClickEvent = [mod: ModData];