<template>
  <div 
    :class="[
      'skeleton',
      `skeleton--${variant}`,
      `skeleton--${animation}`,
      { 'skeleton--rounded': rounded }
    ]"
    :style="customStyles"
    :aria-hidden="true"
    role="presentation"
  >
    <div v-if="variant === 'text'" class="skeleton__content">
      <div 
        v-for="line in lines" 
        :key="line"
        class="skeleton__line"
        :style="{ width: getLineWidth(line) }"
      ></div>
    </div>
    <div v-else-if="variant === 'avatar'" class="skeleton__avatar"></div>
    <div v-else-if="variant === 'card'" class="skeleton__card">
      <div class="skeleton__card-image"></div>
      <div class="skeleton__card-content">
        <div class="skeleton__card-title"></div>
        <div class="skeleton__card-subtitle"></div>
        <div class="skeleton__card-text"></div>
      </div>
    </div>
    <div v-else class="skeleton__content"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  /** Skeleton variant type */
  variant?: 'rectangle' | 'circle' | 'text' | 'avatar' | 'card';
  /** Animation type */
  animation?: 'pulse' | 'wave' | 'shimmer' | 'none';
  /** Width of the skeleton */
  width?: string | number;
  /** Height of the skeleton */
  height?: string | number;
  /** Number of text lines (for text variant) */
  lines?: number;
  /** Whether to apply rounded corners */
  rounded?: boolean;
  /** Custom CSS class */
  class?: string;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'rectangle',
  animation: 'pulse',
  width: '100%',
  height: '20px',
  lines: 3,
  rounded: false,
});

const customStyles = computed(() => {
  const styles: Record<string, string> = {};
  
  if (props.variant !== 'text' && props.variant !== 'card') {
    if (typeof props.width === 'number') {
      styles.width = `${props.width}px`;
    } else {
      styles.width = props.width;
    }
    
    if (typeof props.height === 'number') {
      styles.height = `${props.height}px`;
    } else {
      styles.height = props.height;
    }
  }
  
  return styles;
});

const getLineWidth = (lineNumber: number): string => {
  // Vary line widths for more realistic text skeleton
  const widths = ['100%', '95%', '85%', '90%', '75%'];
  return widths[(lineNumber - 1) % widths.length];
};
</script>

<style scoped>
/* ===== SKELETON BASE STYLES ===== */
.skeleton {
  background: var(--skeleton-bg, var(--charcoal-200));
  border-radius: var(--radius-sm);
  position: relative;
  overflow: hidden;
  display: block;
}

.skeleton--rounded {
  border-radius: 50%;
}

/* ===== SKELETON VARIANTS ===== */
.skeleton--rectangle {
  /* Base rectangle styles already applied */
}

.skeleton--circle {
  border-radius: 50%;
  aspect-ratio: 1;
}

.skeleton--text .skeleton__content {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.skeleton--text .skeleton__line {
  height: 1em;
  background: var(--skeleton-bg, var(--charcoal-200));
  border-radius: var(--radius-sm);
}

.skeleton--avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
}

.skeleton--card {
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.skeleton__card-image {
  width: 100%;
  height: 200px;
  background: var(--skeleton-bg, var(--charcoal-200));
}

.skeleton__card-content {
  padding: var(--space-4);
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.skeleton__card-title {
  height: 24px;
  width: 80%;
  background: var(--skeleton-bg, var(--charcoal-200));
  border-radius: var(--radius-sm);
}

.skeleton__card-subtitle {
  height: 16px;
  width: 60%;
  background: var(--skeleton-bg, var(--charcoal-200));
  border-radius: var(--radius-sm);
}

.skeleton__card-text {
  height: 14px;
  width: 90%;
  background: var(--skeleton-bg, var(--charcoal-200));
  border-radius: var(--radius-sm);
}

/* ===== ANIMATIONS ===== */
.skeleton--pulse {
  animation: skeleton-pulse 1.5s ease-in-out infinite;
}

.skeleton--wave::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    var(--skeleton-highlight, rgba(255, 255, 255, 0.4)),
    transparent
  );
  animation: skeleton-wave 2s infinite;
}

.skeleton--shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    var(--skeleton-highlight, rgba(255, 255, 255, 0.6)),
    transparent
  );
  animation: skeleton-shimmer 1.8s infinite;
}

/* ===== KEYFRAMES ===== */
@keyframes skeleton-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes skeleton-wave {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes skeleton-shimmer {
  0% {
    left: -100%;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .skeleton {
    --skeleton-bg: var(--charcoal-700);
    --skeleton-highlight: rgba(255, 255, 255, 0.1);
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  .skeleton {
    --skeleton-bg: var(--charcoal-400);
    --skeleton-highlight: rgba(255, 255, 255, 0.8);
  }
}

/* ===== REDUCED MOTION SUPPORT ===== */
@media (prefers-reduced-motion: reduce) {
  .skeleton--pulse,
  .skeleton--wave::before,
  .skeleton--shimmer::before {
    animation: none;
  }
  
  .skeleton {
    opacity: 0.7;
  }
}
</style>
