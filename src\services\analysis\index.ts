// Main analysis services index - exports all analysis functionality

// Core analysis services
export * from './core';

// Batch processing services
export * from './batch';

// Unified conflict detection services (consolidated)
export * from './conflicts';
export { UnifiedConflictDetector } from './conflicts/UnifiedConflictDetector';

// Unified dependency analysis (consolidated)
export { UnifiedDependencyAnalyzer } from './dependencies/UnifiedDependencyAnalyzer';

// Resource processing services
export * from './resources';

// Content analysis services
export * from './content';

// Legacy exports for backward compatibility
export { PackageAnalysisService, packageAnalysisService } from './PackageAnalysisService';

// Re-export commonly used types
export type {
    QuickAnalysisResult,
    EnhancedQuickAnalysisResult,
    DetailedAnalysisResult,
    EnhancedDetailedAnalysisResult,
    BatchAnalysisOptions,
    ConflictAnalysisResult,
    CancellationToken
} from '../types/analysis-results';